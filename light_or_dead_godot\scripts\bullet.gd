extends Area2D

@onready var game_manager = GameManager

var direction = Vector2.RIGHT
var speed: float
var damage: int
var lifetime = 3.0  # <PERSON><PERSON>ela zmizí po 3 sekundách

func _ready():
	print("💥 Bullet vytvořena na pozici: ", position)
	
	# Nastavení vlastností
	speed = game_manager.BULLET_SPEED
	damage = game_manager.BULLET_DAMAGE
	
	# Připojení signálů
	connect("body_entered", _on_body_entered)
	connect("area_entered", _on_area_entered)
	
	# Timer pro automatické zničení
	var timer = Timer.new()
	add_child(timer)
	timer.wait_time = lifetime
	timer.one_shot = true
	timer.connect("timeout", _on_lifetime_timeout)
	timer.start()

func _physics_process(delta):
	# Pohyb střely
	position += direction * speed * delta
	
	# Kontrola, zda střela nevyletěla z obrazovky
	if position.x < 0 or position.x > game_manager.SCREEN_WIDTH or \
	   position.y < 0 or position.y > game_manager.SCREEN_HEIGHT:
		print("💥 Bullet vyletěla z obrazovky")
		queue_free()

func set_direction(new_direction: Vector2):
	direction = new_direction.normalized()
	print("   Směr: ", direction)

func _on_body_entered(body):
	print("💥 Bullet zasáhla body: ", body.name)
	
	if body.is_in_group("enemies"):
		print("   Zásah nepřítele!")
		body.take_damage(damage)
		queue_free()

func _on_area_entered(area):
	print("💥 Bullet zasáhla area: ", area.name)

func _on_lifetime_timeout():
	print("💥 Bullet vypršel čas života")
	queue_free()
