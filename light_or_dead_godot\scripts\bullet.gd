extends Area2D

@onready var game_manager = get_node("/root/GameManager")

var speed = 400
var damage = 1
var from_player = true
var direction = Vector2.RIGHT

func _ready():
	# Set the direction based on rotation
	direction = Vector2(cos(rotation), sin(rotation))
	
	# Set up collision detection
	connect("body_entered", Callable(self, "_on_body_entered"))
	connect("area_entered", Callable(self, "_on_area_entered"))
	
	# Set up auto-destruction timer
	var timer = Timer.new()
	add_child(timer)
	timer.wait_time = 2.0  # Bullet lives for 2 seconds
	timer.one_shot = true
	timer.connect("timeout", Callable(self, "_on_lifetime_timeout"))
	timer.start()

func _process(delta):
	# Move the bullet
	position += direction * speed * delta
	
	# Check if bullet is out of screen bounds
	if position.x < 0 or position.x > game_manager.SCREEN_WIDTH or \
	   position.y < 0 or position.y > game_manager.SCREEN_HEIGHT:
		queue_free()

func _on_body_entered(body):
	if body.is_in_group("enemies") and from_player:
		# Player bullet hit enemy
		body.take_damage(damage)
		queue_free()
	elif body.is_in_group("player") and not from_player:
		# Enemy bullet hit player
		body.take_damage(damage)
		queue_free()
	elif body.is_in_group("obstacles"):
		# Hit an obstacle
		queue_free()

func _on_area_entered(area):
	if area.is_in_group("bullets"):
		# Bullets can collide with each other
		if area.from_player != from_player:
			queue_free()

func _on_lifetime_timeout():
	queue_free()
