[gd_scene load_steps=6 format=3 uid="uid://b4n8yxkqjyxe"]

[ext_resource type="Script" uid="uid://bed8biga2pasd" path="res://scripts/player.gd" id="1_player"]
[ext_resource type="Texture2D" uid="uid://dxdu83ax3o5c0" path="res://assets/images/player.png" id="2_player"]

[sub_resource type="CircleShape2D" id="CircleShape2D_1"]
radius = 15.0

[sub_resource type="Gradient" id="Gradient_1"]
colors = PackedColorArray(1, 0.86, 0.59, 1, 0, 0, 0, 0)

[sub_resource type="GradientTexture2D" id="GradientTexture2D_1"]
gradient = SubResource("Gradient_1")
width = 512
height = 512
fill = 1
fill_from = Vector2(0.5, 0.5)

[sub_resource type="CircleShape2D" id="CircleShape2D_2"]
radius = 180.0

[node name="Player" type="CharacterBody2D" groups=["player"]]
script = ExtResource("1_player")

[node name="Sprite2D" type="Sprite2D" parent="."]
texture = ExtResource("2_player")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("CircleShape2D_1")

[node name="PointLight2D" type="PointLight2D" parent="."]
color = Color(1, 0.86, 0.59, 1)
energy = 1.5
shadow_enabled = true
texture = SubResource("GradientTexture2D_1")
texture_scale = 1.8

[node name="LightArea" type="Area2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="LightArea"]
shape = SubResource("CircleShape2D_2")
