[gd_scene load_steps=4 format=3 uid="uid://b4n8yxkqjyxe"]

[ext_resource type="Script" uid="uid://bed8biga2pasd" path="res://scripts/player.gd" id="1_player"]
[ext_resource type="Texture2D" uid="uid://dxdu83ax3o5c0" path="res://assets/images/player.png" id="2_player"]

[sub_resource type="CircleShape2D" id="CircleShape2D_1"]
radius = 15.0

[node name="Player" type="CharacterBody2D" groups=["player"]]
script = ExtResource("1_player")

[node name="Sprite2D" type="Sprite2D" parent="."]
texture = ExtResource("2_player")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("CircleShape2D_1")
