[gd_scene load_steps=5 format=3]

[ext_resource type="Script" path="res://scripts/player.gd" id="1"]

[sub_resource type="CircleShape2D" id="1"]
radius = 15.0

[sub_resource type="Gradient" id="2"]
colors = PackedColorArray(1, 0.862745, 0.588235, 1, 0, 0, 0, 0)

[sub_resource type="GradientTexture2D" id="3"]
gradient = SubResource("2")
width = 512
height = 512
fill = 1
fill_from = Vector2(0.5, 0.5)
fill_to = Vector2(1, 0.5)

[node name="Player" type="CharacterBody2D" groups=["player"]]
script = ExtResource("1")

[node name="Sprite2D" type="Sprite2D" parent="."]
scale = Vector2(0.5, 0.5)

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("1")

[node name="PointLight2D" type="PointLight2D" parent="."]
color = Color(1, 0.862745, 0.588235, 1)
energy = 1.5
shadow_enabled = true
texture = SubResource("3")
texture_scale = 1.8

[node name="WeaponSprite" type="Sprite2D" parent="."]
position = Vector2(15, 0)
scale = Vector2(0.4, 0.4)

[node name="MuzzlePosition" type="Marker2D" parent="WeaponSprite"]
position = Vector2(30, 0)

[node name="ShootTimer" type="Timer" parent="."]
one_shot = true

[node name="StatusEffects" type="Sprite2D" parent="."]
visible = false
modulate = Color(1, 1, 1, 0.5)
scale = Vector2(0.6, 0.6)

[node name="Camera2D" type="Camera2D" parent="."]
current = true
smoothing_enabled = true
