[gd_scene load_steps=4 format=3 uid="uid://evn8yxkqjyxe"]

[ext_resource type="Script" uid="uid://bed8biga2pasd" path="res://scripts/trap.gd" id="1_trap"]
[ext_resource type="Texture2D" uid="uid://dxdu83ax3o5c0" path="res://assets/images/enemy.png" id="2_trap"]

[sub_resource type="CircleShape2D" id="CircleShape2D_1"]
radius = 15.0

[node name="Trap" type="Area2D" groups=["trap"]]
script = ExtResource("1_trap")

[node name="Sprite2D" type="Sprite2D" parent="."]
texture = ExtResource("2_trap")
modulate = Color(1, 0, 0, 1)

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("CircleShape2D_1")
