[gd_scene load_steps=5 format=3]

[ext_resource type="Script" path="res://scripts/trap.gd" id="1"]

[sub_resource type="CircleShape2D" id="1"]
radius = 15.0

[sub_resource type="Animation" id="2"]
resource_name = "trigger"
length = 0.5
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite2D:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.2, 0.5),
"transitions": PackedFloat32Array(0.5, 2, 1),
"update": 0,
"values": [Vector2(0.5, 0.5), Vector2(0.7, 0.7), Vector2(0, 0)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Sprite2D:modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0, 0.5),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Color(1, 1, 1, 1), Color(1, 1, 1, 0)]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_1"]
_data = {
"trigger": SubResource("2")
}

[node name="Trap" type="Area2D" groups=["traps"]]
script = ExtResource("1")

[node name="Sprite2D" type="Sprite2D" parent="."]
scale = Vector2(0.5, 0.5)

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("1")

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
"": SubResource("AnimationLibrary_1")
}
