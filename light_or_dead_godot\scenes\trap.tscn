[gd_scene load_steps=4 format=3 uid="uid://evn8yxkqjyxe"]

[ext_resource type="Script" path="res://scripts/trap.gd" id="1_trap"]
[ext_resource type="Texture2D" path="res://assets/images/trap_spike.png" id="2_trap"]

[sub_resource type="CircleShape2D" id="CircleShape2D_1"]
radius = 15.0

[node name="Trap" type="Area2D" groups=["trap"]]
script = ExtResource("1_trap")

[node name="Sprite2D" type="Sprite2D" parent="."]
texture = ExtResource("2_trap")
modulate = Color(1, 0, 0, 1)

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("CircleShape2D_1")
