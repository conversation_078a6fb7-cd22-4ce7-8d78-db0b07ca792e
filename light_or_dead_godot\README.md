# Light or Dead - Godot Port

Toto je port hry "Light or Dead" do herního enginu Godot.

## Ovládání
- WASD - pohyb
- <PERSON><PERSON> tla<PERSON>ko myši - střelba
- 1, 2, 3 - p<PERSON><PERSON><PERSON><PERSON><PERSON> zbraní
- P - otevření obchodu

## Herní mechaniky
- Přežijte co nejdéle v temnotě
- Zabíjejte nepřátele pro získání světla a bodů
- Nakupujte vylepšení v obchodě
- Pozor na pasti a bosse

## Struktura projektu
- `scenes/` - Her<PERSON><PERSON> scény
- `scripts/` - Skripty
- `assets/` - Herní assety (obrázky, zvuky, fonty)

## Jak spustit
1. Otevřete projekt v Godot Engine
2. Spusťte scénu `splash_screen.tscn`

## Poznámky k portu
Toto je port původní hry vytvořené v Pygame s OpenGL do Godot Engine. Zachovává všechny původní herní mechaniky a přidává moderní vykreslování a fyziku díky možnostem Godot enginu.
