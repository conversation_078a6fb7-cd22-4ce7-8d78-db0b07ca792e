# Light or Dead - Godot Port - IMPLEMENTAČNÍ PLÁN

## FÁZE 1: Příprava projektu
1. ✅ Vyčištění starého projektu
2. ✅ Vytvoření nové struktury adresářů
3. ✅ Vytvoření základního project.godot
4. ✅ Vytvoření placeholder obr<PERSON>z<PERSON>ů

## FÁZE 2: Základní systémy
1. ✅ GameManager singleton - spr<PERSON>va stavu hry
2. ✅ Základní konstanty a enums
3. ✅ Input mapa
4. ✅ Testování základních systémů

## FÁZE 3: <PERSON><PERSON><PERSON> obje<PERSON>y
1. ✅ Player - poh<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, zdraví
2. ✅ Bullet - základní projektily
3. ✅ Enemy - základní nepřítel
4. ✅ Testování interakcí

## FÁZE 4: Herní mechaniky
1. ✅ Světelný systém
2. ✅ Wave systém
3. ✅ Kolizní detekce
4. ✅ Testování gameplay

## FÁZE 5: UI a menu
1. ⏳ Hlavní menu
2. ⏳ HUD ve hře
3. ⏳ Game Over obrazovka
4. ⏳ Testování UI

## FÁZE 6: Pokročilé funkce
1. ✅ Shop systém
2. ✅ Různé zbraně
3. ⏳ Boss nepřátelé
4. ✅ Pasti
5. ⏳ Finální testování

## AKTUÁLNÍ STAV: FÁZE 6 TÉMĚŘ DOKONČENA
Kompletní hra s pokročilými funkcemi!

### Co funguje:
- ✅ **🎬 Splash Screen** - profesionální úvodní obrazovka
- ✅ **📱 Hlavní menu** - Start, Shop, Nastavení, Konec
- ✅ **🛒 Shop systém** - nákup zbraní a vylepšení
- ✅ **🕳️ Pasti** - Poison, Slow, Spike traps s efekty
- ✅ **💡 Světelný systém** - opravený, pomalejší úbytek
- ✅ **🌊 Wave systém** - postupné zvyšování obtížnosti
- ✅ **🔫 Systém zbraní** - Pistol, Shotgun, Machine Gun
- ✅ **⚔️ Status efekty** - otrava, zpomalení s vizuálním feedbackem
- ✅ Player se pohybuje pomocí WASD (s speed multiplierem)
- ✅ Player střílí směrem k myši (různé zbraně)
- ✅ Bullets létají s různými vlastnostmi podle zbraně
- ✅ Enemies se spawní podle vln a pronásledují hráče
- ✅ Kolize mezi bullets a enemies
- ✅ Kolize mezi enemies a player
- ✅ Pokročilé UI (skóre, zdraví, světlo, vlna, zbraň, status)
- ✅ GameManager spravuje kompletní stav hry

### Jak spustit:
1. Otevřete projekt v Godot Engine
2. Spusťte scénu game_scene.tscn
3. Používejte WASD pro pohyb a levé tlačítko myši pro střelbu

### 🕹️ Ovládání:

- **WASD** - pohyb
- **Levé tlačítko myši** - střelba směrem k myši
- **ENTER/SPACE** - přepínání zbraní
- **P** - otevření shopu (ve hře)

### 🎮 Herní mechaniky:

1. **💡 SVĚTLO** - Klíčová mechanika! Světlo se postupně zmenšuje (opraveno - pomalejší úbytek)
2. **🔫 ZBRANĚ** - Začínáte s pistolí. Kupujte další v shopu (Shotgun, Machine Gun)
3. **🌊 VLNY** - Nepřátelé se spawní ve vlnách. Každá vlna je těžší než předchozí
4. **⚔️ BOJE** - Zabíjejte nepřátele pro skóre a světlo. Vyhýbejte se kontaktu!
5. **🛒 SHOP** - Kupujte zbraně a vylepšení za nasbírané body
6. **🕳️ PASTI** - Vyhýbejte se pastím! Otrava, zpomalení, bodce
7. **⚔️ STATUS EFEKTY** - Sledujte své stavy (otrava, zpomalení)

## 🔧 **OPRAVY A OPTIMALIZACE**

### Verze 1.1 - Opravené chyby:
- ✅ Opravena chyba s `Time.get_time_from_start()` → `Time.get_ticks_msec()`
- ✅ Opraveny nepoužité parametry `delta` v `_physics_process()`
- ✅ Přidány kontroly existence GameManageru v Enemy skriptu
- ✅ Optimalizováno volání `setup_for_wave()` pomocí `call_deferred()`

### Verze 2.0 - Kompletní implementace:
- ✅ **Opravený světelný systém** - pomalejší úbytek, správný death threshold
- ✅ **Splash Screen** - profesionální úvodní obrazovka s animacemi
- ✅ **Hlavní menu** - kompletní navigace mezi scénami
- ✅ **Shop systém** - nákup zbraní a vylepšení
- ✅ **Trap systém** - 3 typy pastí s různými efekty
- ✅ **Status efekty** - otrava, zpomalení s vizuálním feedbackem
- ✅ **Vylepšené UI** - status informace, shop přístup

**🎉 KOMPLETNÍ HRA PODLE PYTHON VERZE + VYLEPŠENÍ!** 🚀
