# Light or Dead - Godot Port - IMPLEMENTAČNÍ PLÁN

## FÁZE 1: Příprava projektu
1. ✅ Vyčištění starého projektu
2. ✅ Vytvoření nové struktury adresářů
3. ✅ Vytvoření základního project.godot
4. ✅ Vytvoření placeholder obr<PERSON>zků

## FÁZE 2: Základní systémy
1. ✅ GameManager singleton - spr<PERSON>va stavu hry
2. ✅ Základní konstanty a enums
3. ✅ Input mapa
4. ✅ Testování základních systémů

## FÁZE 3: <PERSON><PERSON><PERSON> objekty
1. ✅ Player - poh<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, zdraví
2. ✅ Bullet - základní projektily
3. ✅ Enemy - základní nepřítel
4. ✅ Testování interakcí

## FÁZE 4: Herní mechaniky
1. ⏳ Světelný systém
2. ⏳ Wave systém
3. ⏳ Kolizn<PERSON> detekce
4. ⏳ Testování gameplay

## FÁZE 5: UI a menu
1. ⏳ Hlavní menu
2. ⏳ HUD ve hře
3. ⏳ Game Over obrazovka
4. ⏳ Testování UI

## FÁZE 6: Pokročilé funkce
1. ⏳ Shop systém
2. ⏳ Různé zbraně
3. ⏳ Boss nepřátelé
4. ⏳ Pasti
5. ⏳ Finální testování

## AKTUÁLNÍ STAV: FÁZE 3 DOKONČENA
Základní herní objekty jsou implementovány a funkční.

### Co funguje:
- ✅ Player se pohybuje pomocí WASD
- ✅ Player střílí směrem k myši
- ✅ Bullets létají a mají kolize
- ✅ Enemies se spawní a pronásledují hráče
- ✅ Kolize mezi bullets a enemies
- ✅ Kolize mezi enemies a player
- ✅ Základní UI (skóre, zdraví)
- ✅ GameManager spravuje stav hry

### Jak spustit:
1. Otevřete projekt v Godot Engine
2. Spusťte scénu game_scene.tscn
3. Používejte WASD pro pohyb a levé tlačítko myši pro střelbu

Nyní pokračuji s FÁZÍ 4 - Herní mechaniky
