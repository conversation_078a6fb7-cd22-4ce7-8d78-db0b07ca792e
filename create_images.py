from PIL import Image, ImageDraw
import os

# Vyt<PERSON><PERSON><PERSON><PERSON>, pokud neexistuje
os.makedirs("light_or_dead_godot/assets/images", exist_ok=True)

# Funkce pro vytvoření k<PERSON> o<PERSON>r<PERSON>z<PERSON>
def create_circle_image(filename, size, color, border_color=None, border_width=0):
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # <PERSON>ykres<PERSON><PERSON> kruhu
    if border_color and border_width > 0:
        draw.ellipse((border_width, border_width, size - border_width, size - border_width), fill=color)
        draw.ellipse((0, 0, size, size), outline=border_color, width=border_width)
    else:
        draw.ellipse((0, 0, size, size), fill=color)
    
    img.save(f"light_or_dead_godot/assets/images/{filename}.png")
    print(f"Created {filename}.png")

# Funkce pro vytvoř<PERSON>í obd<PERSON><PERSON><PERSON>ové<PERSON> o<PERSON>r<PERSON>
def create_rectangle_image(filename, width, height, color, border_color=None, border_width=0):
    img = Image.new('RGBA', (width, height), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Vykreslení obdélníku
    if border_color and border_width > 0:
        draw.rectangle((border_width, border_width, width - border_width, height - border_width), fill=color)
        draw.rectangle((0, 0, width - 1, height - 1), outline=border_color, width=border_width)
    else:
        draw.rectangle((0, 0, width - 1, height - 1), fill=color)
    
    img.save(f"light_or_dead_godot/assets/images/{filename}.png")
    print(f"Created {filename}.png")

# Vytvoření ikony
create_rectangle_image("icon", 64, 64, (255, 220, 150, 255), (0, 0, 0, 255), 2)

# Vytvoření hráče
create_circle_image("player", 64, (255, 255, 255, 255), (200, 200, 200, 255), 2)

# Vytvoření zbraní
create_rectangle_image("weapon_pistol", 40, 20, (200, 200, 0, 255))
create_rectangle_image("weapon_shotgun", 50, 25, (200, 150, 0, 255))
create_rectangle_image("weapon_machinegun", 60, 15, (0, 200, 200, 255))

# Vytvoření nepřátel
create_circle_image("enemy_basic", 48, (255, 0, 0, 255))
create_circle_image("enemy_fast", 32, (255, 100, 0, 255))
create_circle_image("enemy_boss", 96, (255, 0, 255, 255), (200, 0, 200, 255), 3)

# Vytvoření střel
create_circle_image("bullet_basic", 16, (255, 255, 0, 255))
create_circle_image("bullet_shotgun", 20, (255, 150, 0, 255))
create_circle_image("bullet_boss", 24, (255, 0, 255, 255))

# Vytvoření pastí
create_circle_image("trap_spike", 48, (180, 180, 180, 255), (100, 100, 100, 255), 2)
create_circle_image("trap_poison", 48, (0, 180, 0, 255), (0, 100, 0, 255), 2)
create_circle_image("trap_slow", 48, (100, 100, 255, 255), (50, 50, 200, 255), 2)

print("All images created successfully!")
