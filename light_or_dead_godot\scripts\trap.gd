extends Area2D

@onready var game_manager = get_node("/root/GameManager")
@onready var sprite = $Sprite2D
@onready var animation_player = $AnimationPlayer

var trap_type = 0  # 0=spike, 1=poison, 2=slow
var triggered = false
var damage = 0
var duration = 0.0

func _ready():
	connect("body_entered", Callable(self, "_on_body_entered"))
	
	# Set up trap based on type
	match trap_type:
		game_manager.TrapType.SPIKE:
			sprite.modulate = Color(0.7, 0.7, 0.7)  # Gray for spikes
			damage = game_manager.TRAP_SPIKE_DAMAGE
		
		game_manager.TrapType.POISON:
			sprite.modulate = Color(0, 0.7, 0)  # Green for poison
			damage = game_manager.TRAP_POISON_DAMAGE
			duration = game_manager.TRAP_POISON_DURATION
		
		game_manager.TrapType.SLOW:
			sprite.modulate = Color(0.4, 0.4, 1.0)  # Blue for slow
			duration = game_manager.TRAP_SLOW_DURATION
	
	# Add to traps group
	add_to_group("traps")

func _on_body_entered(body):
	if body.is_in_group("player") and not triggered:
		trigger_trap(body)

func trigger_trap(player):
	triggered = true
	
	match trap_type:
		game_manager.TrapType.SPIKE:
			# Immediate damage
			player.take_damage(damage)
			animation_player.play("trigger")
		
		game_manager.TrapType.POISON:
			# Apply poison effect
			player.apply_poison()
			animation_player.play("trigger")
		
		game_manager.TrapType.SLOW:
			# Apply slow effect
			player.apply_slow()
			animation_player.play("trigger")
	
	# Play sound effect
	# $TriggerSound.play()

func _on_animation_finished(anim_name):
	if anim_name == "trigger":
		queue_free()
