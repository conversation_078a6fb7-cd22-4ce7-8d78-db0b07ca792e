extends Area2D

@onready var sprite = $Sprite2D
@onready var collision_shape = $CollisionShape2D
@onready var game_manager = GameManager

var trap_type: GameManager.TrapType
var activated = false
var activation_timer = 0.0
const ACTIVATION_DELAY = 0.5  # Zpoždění před aktivací

func _ready():
	print("🕳️ Trap vytvořena")
	
	# Připojení signálu pro kolizi s hráčem
	body_entered.connect(_on_body_entered)
	
	# Nastavení velikosti
	scale = Vector2(1.0, 1.0)

func setup_trap(type: GameManager.TrapType):
	trap_type = type
	
	# Nastavení vzhledu podle typu
	match trap_type:
		GameManager.TrapType.POISON:
			sprite.modulate = Color.GREEN
			print("   Typ: Poison Trap")
		GameManager.TrapType.SLOW:
			sprite.modulate = Color.BLUE
			print("   Typ: Slow Trap")
		GameManager.TrapType.SPIKE:
			sprite.modulate = Color.RED
			print("   Typ: Spike Trap")

func _process(delta):
	if activated:
		activation_timer += delta
		
		# <PERSON>lik<PERSON><PERSON> před aktivací
		if activation_timer < ACTIVATION_DELAY:
			var blink_speed = 10.0
			var alpha = 0.5 + 0.5 * sin(activation_timer * blink_speed)
			sprite.modulate.a = alpha
		else:
			# Aktivace pasti
			trigger_trap()

func _on_body_entered(body):
	if body.is_in_group("player") and not activated:
		print("🕳️ Hráč vstoupil na past!")
		activated = true
		activation_timer = 0.0

func trigger_trap():
	print("💥 Past aktivována! Typ: ", trap_type)
	
	match trap_type:
		GameManager.TrapType.POISON:
			game_manager.apply_poison()
			print("☠️ Poison trap aktivována!")
		
		GameManager.TrapType.SLOW:
			game_manager.apply_slow()
			print("🐌 Slow trap aktivována!")
		
		GameManager.TrapType.SPIKE:
			game_manager.damage_player(game_manager.SPIKE_TRAP_DAMAGE)
			print("🗡️ Spike trap aktivována! Poškození: ", game_manager.SPIKE_TRAP_DAMAGE)
	
	# Vizuální efekt aktivace
	var tween = create_tween()
	tween.tween_property(sprite, "scale", Vector2(1.5, 1.5), 0.2)
	tween.tween_property(sprite, "scale", Vector2(0.0, 0.0), 0.3)
	tween.tween_callback(queue_free)
