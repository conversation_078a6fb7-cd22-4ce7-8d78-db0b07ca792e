extends Node2D

@onready var game_manager = GameManager

func _ready():
	print("🧪 Testovací scéna spuštěna")
	
	# Test GameManageru
	print("🔍 Testování GameManageru...")
	print("   Aktuální stav: ", game_manager.current_state)
	print("   Skóre: ", game_manager.score)
	print("   Zdrav<PERSON> hráče: ", game_manager.player_health)
	
	# Test změny stavu
	game_manager.change_state(GameManager.GameState.PLAYING)
	
	# Test přidání skóre
	game_manager.add_score(100)
	
	# Test poškození hráče
	game_manager.damage_player(20)
	
	print("✅ Základní testy dokončeny")

func _input(event):
	if event.is_action_pressed("shoot"):
		print("🔫 Střelba detekována!")
	
	if Input.is_action_pressed("move_up"):
		print("⬆️ Pohyb nahoru")
	if Input.is_action_pressed("move_down"):
		print("⬇️ Pohyb dolů")
	if Input.is_action_pressed("move_left"):
		print("⬅️ Pohyb vlevo")
	if Input.is_action_pressed("move_right"):
		print("➡️ Pohyb vpravo")
