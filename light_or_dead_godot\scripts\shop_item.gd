extends Panel

signal item_purchased(item_id)

@onready var name_label = $NameLabel
@onready var cost_label = $CostLabel
@onready var description_label = $DescriptionLabel
@onready var buy_button = $BuyButton
@onready var owned_label = $OwnedLabel
@onready var animation_player = $AnimationPlayer

var item_id = ""
var item_name = ""
var item_cost = 0
var item_description = ""
var can_afford = false
var owned = false

func _ready():
	# Set up the item display
	name_label.text = item_name
	cost_label.text = str(item_cost)
	description_label.text = item_description
	
	# Connect the buy button
	buy_button.connect("pressed", Callable(self, "_on_BuyButton_pressed"))
	
	# Update the appearance
	update_appearance()

func update_appearance():
	# Update owned status
	owned_label.visible = owned
	buy_button.visible = !owned
	
	# Update affordability
	if owned:
		# Already owned
		modulate = Color(0.7, 0.7, 0.7)
	elif can_afford:
		# Can afford
		modulate = Color(1, 1, 1)
		buy_button.disabled = false
	else:
		# Cannot afford
		modulate = Color(0.5, 0.5, 0.5)
		buy_button.disabled = true

func _on_BuyButton_pressed():
	if can_afford and !owned:
		# Emit the purchase signal
		emit_signal("item_purchased", item_id)

func play_purchase_effect():
	# Play a purchase animation
	animation_player.play("purchase")
