extends Control

@onready var start_button = $VBoxContainer/StartButton
@onready var shop_button = $VBoxContainer/ShopButton
@onready var settings_button = $VBoxContainer/SettingsButton
@onready var quit_button = $VBoxContainer/QuitButton
@onready var high_score_label = $HighScoreLabel
@onready var version_label = $VersionLabel

var high_score = 0

func _ready():
	print("📱 Hlavní menu spuštěno")
	
	# Načtení high score
	load_high_score()
	
	# Nastavení textu
	high_score_label.text = "Nejlepší skóre: " + str(high_score)
	version_label.text = "v1.1.0"
	
	# Připojení signálů
	start_button.pressed.connect(_on_start_pressed)
	shop_button.pressed.connect(_on_shop_pressed)
	settings_button.pressed.connect(_on_settings_pressed)
	quit_button.pressed.connect(_on_quit_pressed)
	
	# Nastavení GameManageru
	GameManager.change_state(GameManager.GameState.MENU)

func load_high_score():
	# Načtení high score ze souboru
	var file = FileAccess.open("user://high_score.save", FileAccess.READ)
	if file:
		high_score = file.get_32()
		file.close()
	else:
		high_score = 0

func save_high_score():
	# Uložení high score do souboru
	var file = FileAccess.open("user://high_score.save", FileAccess.WRITE)
	if file:
		file.store_32(high_score)
		file.close()

func update_high_score(new_score: int):
	if new_score > high_score:
		high_score = new_score
		save_high_score()
		high_score_label.text = "Nejlepší skóre: " + str(high_score)

func _on_start_pressed():
	print("🎮 Spuštění hry")
	GameManager.reset_game()
	get_tree().change_scene_to_file("res://scenes/game_scene.tscn")

func _on_shop_pressed():
	print("🛒 Otevření shopu")
	get_tree().change_scene_to_file("res://scenes/shop.tscn")

func _on_settings_pressed():
	print("⚙️ Otevření nastavení")
	get_tree().change_scene_to_file("res://scenes/settings.tscn")

func _on_quit_pressed():
	print("👋 Ukončení hry")
	get_tree().quit()
