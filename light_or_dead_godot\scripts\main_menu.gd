extends Control

@onready var game_manager = get_node("/root/GameManager")
@onready var start_button = $VBoxContainer/StartButton
@onready var settings_button = $VBoxContainer/SettingsButton
@onready var quit_button = $VBoxContainer/QuitButton
@onready var high_score_label = $HighScoreLabel
@onready var version_label = $VersionLabel
@onready var animation_player = $AnimationPlayer

func _ready():
	# Set up button connections
	start_button.connect("pressed", Callable(self, "_on_StartButton_pressed"))
	settings_button.connect("pressed", Callable(self, "_on_SettingsButton_pressed"))
	quit_button.connect("pressed", Callable(self, "_on_QuitButton_pressed"))
	
	# Update high score display
	high_score_label.text = "High Score: " + str(game_manager.high_score)
	
	# Play background animation
	animation_player.play("background_animation")

func _on_StartButton_pressed():
	# Reset game state for a new game
	game_manager.reset_game()
	
	# Change game state and scene
	game_manager.change_game_state(game_manager.GameState.PLAYING)
	get_tree().change_scene_to_file("res://scenes/game_scene.tscn")

func _on_SettingsButton_pressed():
	# Change to settings scene
	game_manager.change_game_state(game_manager.GameState.SETTINGS)
	get_tree().change_scene_to_file("res://scenes/settings_scene.tscn")

func _on_QuitButton_pressed():
	# Quit the game
	get_tree().quit()
