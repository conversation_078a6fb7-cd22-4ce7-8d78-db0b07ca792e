extends Control

@onready var game_manager = get_node("/root/GameManager")
@onready var logo_label = $LogoLabel
@onready var subtitle_label = $SubtitleLabel
@onready var click_label = $ClickLabel
@onready var animation_player = $AnimationPlayer
@onready var timer = $Timer

var splash_duration = 5.0
var can_proceed = false

func _ready():
	# Start the fade-in animation
	animation_player.play("fade_in")

	# Start the timer for automatic transition
	timer.wait_time = splash_duration
	timer.one_shot = true
	timer.start()

	# Connect the timer signal
	timer.connect("timeout", Callable(self, "_on_Timer_timeout"))

func _process(_delta):
	# Make the click text pulse
	var pulse_value = 0.7 + 0.3 * sin(Time.get_ticks_msec() * 0.005)
	click_label.modulate.a = pulse_value

	# Check for click to proceed
	if can_proceed and Input.is_action_just_pressed("shoot"):
		proceed_to_main_menu()

func _input(event):
	# Any click or key press will proceed after the fade-in is complete
	if can_proceed and (event is InputEventMouseButton or event is InputEventKey):
		if event.pressed:
			proceed_to_main_menu()

func _on_Timer_timeout():
	can_proceed = true

func _on_AnimationPlayer_animation_finished(anim_name):
	if anim_name == "fade_in":
		can_proceed = true
	elif anim_name == "fade_out":
		# Change to the main menu scene
		get_tree().change_scene_to_file("res://scenes/main_menu.tscn")

func proceed_to_main_menu():
	# Play the fade-out animation
	animation_player.play("fade_out")

	# Change the game state
	game_manager.change_game_state(game_manager.GameState.MAIN_MENU)
