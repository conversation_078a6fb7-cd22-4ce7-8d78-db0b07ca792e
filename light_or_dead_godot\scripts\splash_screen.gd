extends Control

@onready var logo_label = $VBoxContainer/LogoLabel
@onready var subtitle_label = $VBoxContainer/SubtitleLabel
@onready var click_label = $VBoxContainer/ClickLabel
@onready var animation_player = $AnimationPlayer

var splash_timer = 0.0
const SPLASH_DURATION = 5.0
const AUTO_ADVANCE_TIME = 3.0

func _ready():
	print("🎬 Splash Screen spuštěn")

	# Nastavení textu
	logo_label.text = "LIGHT OR DEAD"
	subtitle_label.text = "Enhanced Edition"
	click_label.text = "Klikněte kamkoliv pro pokračování"

	# Spuštění animace (s kontrolou)
	if animation_player:
		var library = animation_player.get_animation_library("default")
		if library and library.has_animation("fade_in"):
			animation_player.play("fade_in")
			print("   ✅ Animace fade_in spuštěna")
		else:
			print("   ❌ Animace fade_in nenalezena - přeskakuji")
			# Ruční fade-in
			logo_label.modulate.a = 0.0
			subtitle_label.modulate.a = 0.0
			var tween = create_tween()
			tween.parallel().tween_property(logo_label, "modulate:a", 1.0, 1.0)
			tween.parallel().tween_property(subtitle_label, "modulate:a", 1.0, 1.5)

	# Skrytí click labelu na začátku
	click_label.modulate.a = 0.0

func _process(delta):
	splash_timer += delta

	# Zobrazení click labelu po určitém čase
	if splash_timer > AUTO_ADVANCE_TIME and click_label.modulate.a < 1.0:
		click_label.modulate.a = min(1.0, click_label.modulate.a + delta * 2.0)

	# Automatický přechod po SPLASH_DURATION
	if splash_timer > SPLASH_DURATION:
		go_to_main_menu()

func _input(event):
	# Přechod na kliknutí nebo stisk klávesy
	if event is InputEventMouseButton and event.pressed:
		go_to_main_menu()
	elif event is InputEventKey and event.pressed:
		go_to_main_menu()

func go_to_main_menu():
	print("🎬 Přechod do hlavního menu")

	# Fade out efekt
	var tween = create_tween()
	tween.tween_property(self, "modulate:a", 0.0, 0.5)
	tween.tween_callback(change_scene)

func change_scene():
	get_tree().change_scene_to_file("res://scenes/main_menu.tscn")
