[res://scripts/player.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 65,
"scroll_position": 65.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://README.md]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 0,
"scroll_position": 0.0,
"selection": false,
"syntax_highlighter": "Markdown"
}

[res://scripts/enemy.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 75,
"scroll_position": 75.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://scripts/game_scene.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 90,
"scroll_position": 90.0,
"selection": false,
"syntax_highlighter": "GDScript"
}
