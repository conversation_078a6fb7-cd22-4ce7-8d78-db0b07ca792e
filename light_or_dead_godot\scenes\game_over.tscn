[gd_scene load_steps=4 format=3]

[ext_resource type="Script" path="res://scripts/game_over.gd" id="1"]

[sub_resource type="Animation" id="1"]
resource_name = "fade_in"
length = 1.0
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath(".:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 1),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Color(1, 1, 1, 0), Color(1, 1, 1, 1)]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_1"]
_data = {
"fade_in": SubResource("1")
}

[node name="GameOver" type="Control"]
modulate = Color(1, 1, 1, 0)
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1")

[node name="Background" type="ColorRect" parent="."]
layout_mode = 0
anchor_right = 1.0
anchor_bottom = 1.0
color = Color(0.05, 0.05, 0.1, 0.9)

[node name="GameOverLabel" type="Label" parent="."]
layout_mode = 0
anchor_left = 0.5
anchor_top = 0.3
anchor_right = 0.5
anchor_bottom = 0.3
offset_left = -200.0
offset_top = -50.0
offset_right = 200.0
offset_bottom = 0.0
theme_override_colors/font_color = Color(1, 0, 0, 1)
theme_override_font_sizes/font_size = 48
text = "GAME OVER"
horizontal_alignment = 1

[node name="ScoreLabel" type="Label" parent="."]
layout_mode = 0
anchor_left = 0.5
anchor_top = 0.4
anchor_right = 0.5
anchor_bottom = 0.4
offset_left = -100.0
offset_top = 0.0
offset_right = 100.0
offset_bottom = 30.0
theme_override_font_sizes/font_size = 24
text = "Score: 0"
horizontal_alignment = 1

[node name="HighScoreLabel" type="Label" parent="."]
layout_mode = 0
anchor_left = 0.5
anchor_top = 0.4
anchor_right = 0.5
anchor_bottom = 0.4
offset_left = -100.0
offset_top = 40.0
offset_right = 100.0
offset_bottom = 70.0
theme_override_font_sizes/font_size = 24
text = "High Score: 0"
horizontal_alignment = 1

[node name="WaveLabel" type="Label" parent="."]
layout_mode = 0
anchor_left = 0.5
anchor_top = 0.4
anchor_right = 0.5
anchor_bottom = 0.4
offset_left = -100.0
offset_top = 80.0
offset_right = 100.0
offset_bottom = 110.0
theme_override_font_sizes/font_size = 24
text = "Wave: 1"
horizontal_alignment = 1

[node name="RetryButton" type="Button" parent="."]
layout_mode = 0
anchor_left = 0.5
anchor_top = 0.6
anchor_right = 0.5
anchor_bottom = 0.6
offset_left = -100.0
offset_top = 0.0
offset_right = 100.0
offset_bottom = 40.0
theme_override_font_sizes/font_size = 20
text = "Retry"

[node name="MainMenuButton" type="Button" parent="."]
layout_mode = 0
anchor_left = 0.5
anchor_top = 0.6
anchor_right = 0.5
anchor_bottom = 0.6
offset_left = -100.0
offset_top = 60.0
offset_right = 100.0
offset_bottom = 100.0
theme_override_font_sizes/font_size = 20
text = "Main Menu"

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
"": SubResource("AnimationLibrary_1")
}
