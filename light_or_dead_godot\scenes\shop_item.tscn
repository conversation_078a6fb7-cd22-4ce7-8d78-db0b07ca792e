[gd_scene load_steps=4 format=3]

[ext_resource type="Script" path="res://scripts/shop_item.gd" id="1"]

[sub_resource type="Animation" id="1"]
resource_name = "purchase"
length = 0.5
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath(".:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.2, 0.5),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Color(1, 1, 1, 1), Color(0, 1, 0, 1), Color(0.7, 0.7, 0.7, 1)]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_1"]
_data = {
"purchase": SubResource("1")
}

[node name="ShopItem" type="Panel"]
custom_minimum_size = Vector2(200, 80)
offset_right = 200.0
offset_bottom = 80.0
script = ExtResource("1")

[node name="NameLabel" type="Label" parent="."]
layout_mode = 0
anchor_right = 1.0
offset_top = 5.0
offset_bottom = 25.0
text = "Item Name"
horizontal_alignment = 1

[node name="CostLabel" type="Label" parent="."]
layout_mode = 0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_top = -25.0
offset_bottom = -5.0
text = "100"
horizontal_alignment = 1

[node name="DescriptionLabel" type="Label" parent="."]
layout_mode = 0
anchor_right = 1.0
anchor_bottom = 1.0
offset_top = 25.0
offset_bottom = -25.0
theme_override_font_sizes/font_size = 12
text = "Item description"
horizontal_alignment = 1
vertical_alignment = 1

[node name="BuyButton" type="Button" parent="."]
layout_mode = 0
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -40.0
offset_top = -30.0
offset_right = 40.0
offset_bottom = -5.0
text = "Buy"

[node name="OwnedLabel" type="Label" parent="."]
visible = false
layout_mode = 0
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -40.0
offset_top = -30.0
offset_right = 40.0
offset_bottom = -5.0
theme_override_colors/font_color = Color(0, 1, 0, 1)
text = "OWNED"
horizontal_alignment = 1
vertical_alignment = 1

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
"": SubResource("AnimationLibrary_1")
}
