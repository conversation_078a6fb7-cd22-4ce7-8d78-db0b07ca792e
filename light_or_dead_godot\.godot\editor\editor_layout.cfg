[docks]

dock_3_selected_tab_idx=0
dock_4_selected_tab_idx=0
dock_5_selected_tab_idx=0
dock_floating={}
dock_filesystem_h_split_offset=240
dock_filesystem_v_split_offset=0
dock_filesystem_display_mode=0
dock_filesystem_file_sort=0
dock_filesystem_file_list_display_mode=1
dock_filesystem_selected_paths=PackedStringArray("res://scenes/")
dock_filesystem_uncollapsed_paths=PackedStringArray("Favorites", "res://")
dock_node_current_tab=0
dock_history_include_scene=true
dock_history_include_global=true
dock_bottom=[]
dock_closed=[]
dock_split_2=0
dock_split_3=0
dock_hsplit_1=0
dock_hsplit_2=270
dock_hsplit_3=-270
dock_hsplit_4=0
dock_3="Scene,Import"
dock_4="FileSystem"
dock_5="Inspector,Node,History"

[EditorNode]

open_scenes=PackedStringArray("res://scenes/game_scene.tscn")
current_scene="res://scenes/game_scene.tscn"
center_split_offset=-734
selected_default_debugger_tab_idx=1
selected_main_editor_idx=0
selected_bottom_panel_item=1

[EditorWindow]

screen=0
mode="maximized"
position=Vector2i(0, 23)

[ScriptEditor]

open_scripts=["res://scripts/enemy.gd", "res://scripts/game_scene.gd", "res://scripts/player.gd", "res://README.md"]
selected_script="res://scripts/player.gd"
open_help=[]
script_split_offset=200
list_split_offset=0
zoom_factor=1.0

[GameView]

floating_window_rect=Rect2i(509, 140, 812, 647)
floating_window_screen=0

[ShaderEditor]

open_shaders=[]
split_offset=200
selected_shader=""
text_shader_zoom_factor=1.0

[editor_log]

log_filter_0=true
log_filter_2=true
log_filter_1=false
log_filter_3=true
log_filter_4=true
collapse=false
show_search=true
