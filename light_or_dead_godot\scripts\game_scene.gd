extends Node2D

@onready var game_manager = get_node("/root/GameManager")
@onready var player = $Player
@onready var wave_label = $UI/WaveLabel
@onready var score_label = $UI/ScoreLabel
@onready var health_bar = $UI/HealthBar
@onready var light_bar = $UI/LightBar
@onready var wave_timer = $WaveTimer
@onready var spawn_timer = $SpawnTimer
@onready var wave_message_timer = $WaveMessageTimer

var enemies = []
var traps = []
var current_wave = 0
var enemies_to_spawn = 0
var spawn_positions = []

func _ready():
	# Initialize spawn positions (edges of the screen)
	var margin = 50
	var screen_width = game_manager.SCREEN_WIDTH
	var screen_height = game_manager.SCREEN_HEIGHT
	
	# Top edge
	for x in range(margin, screen_width - margin, 100):
		spawn_positions.append(Vector2(x, -margin))
	
	# Bottom edge
	for x in range(margin, screen_width - margin, 100):
		spawn_positions.append(Vector2(x, screen_height + margin))
	
	# Left edge
	for y in range(margin, screen_height - margin, 100):
		spawn_positions.append(Vector2(-margin, y))
	
	# Right edge
	for y in range(margin, screen_height - margin, 100):
		spawn_positions.append(Vector2(screen_width + margin, y))
	
	# Start the first wave
	start_wave(1)

func _process(delta):
	# Update UI
	score_label.text = "Score: " + str(game_manager.score)
	health_bar.value = game_manager.player_stats["health"]
	health_bar.max_value = game_manager.player_stats["max_health"]
	light_bar.value = game_manager.player_stats["light_radius"]
	light_bar.min_value = game_manager.MIN_LIGHT_RADIUS
	light_bar.max_value = game_manager.MAX_LIGHT_RADIUS
	
	# Handle wave transition
	if game_manager.wave_transition_timer > 0:
		game_manager.wave_transition_timer -= delta
		if game_manager.wave_transition_timer <= 0:
			start_wave(current_wave + 1)
	
	# Handle wave message
	if game_manager.wave_message_timer > 0:
		game_manager.wave_message_timer -= delta
		wave_label.text = game_manager.wave_message_text
		wave_label.visible = true
	else:
		wave_label.visible = false
	
	# Handle weapon switching
	if Input.is_action_just_pressed("weapon_1") and 0 < game_manager.player_stats["weapons"].size():
		player.switch_weapon(0)
	
	if Input.is_action_just_pressed("weapon_2") and 1 < game_manager.player_stats["weapons"].size():
		player.switch_weapon(1)
	
	if Input.is_action_just_pressed("weapon_3") and 2 < game_manager.player_stats["weapons"].size():
		player.switch_weapon(2)
	
	# Handle shop key
	if Input.is_action_just_pressed("shop"):
		game_manager.change_game_state(game_manager.GameState.SHOP)
		get_tree().change_scene_to_file("res://scenes/shop_scene.tscn")

func start_wave(wave_num):
	current_wave = wave_num
	game_manager.current_wave = wave_num
	
	# Clear any remaining enemies and traps
	for enemy in get_tree().get_nodes_in_group("enemies"):
		enemy.queue_free()
	
	for trap in get_tree().get_nodes_in_group("traps"):
		trap.queue_free()
	
	# Determine if this is a boss wave
	var is_boss_wave = (wave_num > 0 and wave_num % 5 == 0)
	
	if is_boss_wave:
		# Boss wave
		game_manager.enemies_to_spawn_in_wave = 1
		game_manager.enemies_alive_in_wave = 1
		game_manager.wave_message_text = "WAVE " + str(wave_num) + " - BOSS WAVE!"
		game_manager.wave_message_timer = game_manager.WAVE_MESSAGE_DURATION
		
		# Spawn the boss
		spawn_boss()
	else:
		# Regular wave
		var num_enemies = 5 + wave_num * 2
		game_manager.enemies_to_spawn_in_wave = num_enemies
		game_manager.enemies_alive_in_wave = num_enemies
		game_manager.wave_message_text = "WAVE " + str(wave_num)
		game_manager.wave_message_timer = game_manager.WAVE_MESSAGE_DURATION
		
		# Start spawning enemies
		enemies_to_spawn = num_enemies
		spawn_timer.start()
		
		# Spawn traps
		spawn_traps()

func spawn_enemy():
	if enemies_to_spawn <= 0:
		return
	
	var enemy = preload("res://scenes/enemy.tscn").instantiate()
	add_child(enemy)
	
	# Choose a random spawn position
	var spawn_pos = spawn_positions[randi() % spawn_positions.size()]
	enemy.position = spawn_pos
	
	# Set enemy properties based on current wave
	enemy.max_health = game_manager.ENEMY_BASE_HEALTH + int(current_wave * 0.8)
	enemy.health = enemy.max_health
	enemy.speed = game_manager.ENEMY_BASE_SPEED + current_wave * 4.0 + (current_wave / 4) * 5.0
	enemy.damage = game_manager.ENEMY_BASE_DAMAGE + int(current_wave * 0.5)
	enemy.score_value = game_manager.ENEMY_BASE_SCORE + current_wave
	
	enemies_to_spawn -= 1
	if enemies_to_spawn > 0:
		spawn_timer.start()

func spawn_boss():
	var boss = preload("res://scenes/boss.tscn").instantiate()
	add_child(boss)
	
	# Spawn at the top center of the screen
	boss.position = Vector2(game_manager.SCREEN_WIDTH / 2, -50)
	
	# Calculate boss stats based on wave and player score
	var base_health = game_manager.ENEMY_BASE_HEALTH + int(current_wave * 0.8)
	var score_hp_bonus = int((game_manager.score / 100) * 0.1)  # +1 HP per 1000 score
	var boss_health = int(base_health * 15) + score_hp_bonus
	
	boss.max_health = max(boss_health, base_health * 5)  # Ensure minimum HP
	boss.health = boss.max_health
	boss.speed = (game_manager.ENEMY_BASE_SPEED + current_wave * 4.0) * 0.8
	boss.damage = int((game_manager.ENEMY_BASE_DAMAGE + int(current_wave * 0.5)) * 2.5)
	boss.score_value = (game_manager.ENEMY_BASE_SCORE + current_wave) * 10
	boss.is_boss = true

func spawn_traps():
	# Generate traps for this wave
	var num_traps = randi_range(1, 3 + current_wave / 2)
	
	for i in range(num_traps):
		if randf() < game_manager.TRAP_SPAWN_CHANCE:
			var trap = preload("res://scenes/trap.tscn").instantiate()
			add_child(trap)
			
			# Find a safe position away from the player
			var safe_distance = 150
			var trap_pos = Vector2.ZERO
			var valid_pos = false
			
			while not valid_pos:
				trap_pos = Vector2(
					randf_range(50, game_manager.SCREEN_WIDTH - 50),
					randf_range(50, game_manager.SCREEN_HEIGHT - 50)
				)
				
				var dist_to_player = trap_pos.distance_to(player.position)
				if dist_to_player > safe_distance:
					valid_pos = true
			
			trap.position = trap_pos
			
			# Set random trap type
			trap.trap_type = randi() % 3  # 0=spike, 1=poison, 2=slow

func _on_SpawnTimer_timeout():
	spawn_enemy()

func _on_WaveTimer_timeout():
	# This is used for wave transitions
	start_wave(current_wave + 1)
