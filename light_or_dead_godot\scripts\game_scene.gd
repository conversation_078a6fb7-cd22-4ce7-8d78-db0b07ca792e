extends Node2D

@onready var game_manager = GameManager
@onready var ui_layer = $UILayer
@onready var score_label = $UILayer/ScoreLabel
@onready var health_label = $UILayer/HealthLabel

var enemy_spawn_timer = 0.0
var enemy_spawn_interval = 3.0  # Spawn nepřítele každé 3 sekundy

func _ready():
	print("🎮 Herní scéna spuštěna")
	game_manager.reset_game()
	
	# Spawn hráče
	spawn_player()
	
	# Spawn prvního nepřítele
	spawn_enemy()

func _process(delta):
	# Update UI
	if score_label:
		score_label.text = "Skóre: " + str(game_manager.score)
	
	if health_label:
		var player = get_tree().get_first_node_in_group("player")
		if player:
			health_label.text = "Zdraví: " + str(player.health) + "/" + str(player.max_health)
		else:
			health_label.text = "Zdraví: 0/100"
	
	# Spawn nepřátel
	enemy_spawn_timer += delta
	if enemy_spawn_timer >= enemy_spawn_interval:
		enemy_spawn_timer = 0.0
		spawn_enemy()
	
	# Kontrola stavu hry
	if game_manager.current_state == GameManager.GameState.GAME_OVER:
		print("🎮 Hra skončila!")
		# Zde by se měla načíst Game Over scéna

func spawn_player():
	print("👤 Spawn hráče")
	var player_scene = preload("res://scenes/player.tscn")
	var player = player_scene.instantiate()
	add_child(player)

func spawn_enemy():
	print("👹 Spawn nepřítele")
	var enemy_scene = preload("res://scenes/enemy.tscn")
	var enemy = enemy_scene.instantiate()
	
	# Náhodná pozice na okraji obrazovky
	var spawn_side = randi() % 4
	var spawn_pos = Vector2.ZERO
	
	match spawn_side:
		0:  # Horní strana
			spawn_pos = Vector2(randf() * game_manager.SCREEN_WIDTH, -50)
		1:  # Pravá strana
			spawn_pos = Vector2(game_manager.SCREEN_WIDTH + 50, randf() * game_manager.SCREEN_HEIGHT)
		2:  # Dolní strana
			spawn_pos = Vector2(randf() * game_manager.SCREEN_WIDTH, game_manager.SCREEN_HEIGHT + 50)
		3:  # Levá strana
			spawn_pos = Vector2(-50, randf() * game_manager.SCREEN_HEIGHT)
	
	enemy.position = spawn_pos
	add_child(enemy)
