extends Node2D

@onready var game_manager = GameManager
@onready var ui_layer = $UILayer
# UI ELEMENTY PODLE PYTHON VERZE
@onready var score_label = $UILayer/ScoreLabel
@onready var health_label = $UILayer/HealthLabel
@onready var hp_bar = $UILayer/HPBar
@onready var light_label = $UILayer/LightLabel
@onready var wave_label = $UILayer/WaveLabel
@onready var weapon_label = $UILayer/WeaponLabel
@onready var weapon_keys_label = $UILayer/WeaponKeysLabel
@onready var status_label = $UILayer/StatusLabel

var enemy_spawn_timer = 0.0
var enemy_spawn_interval = 2.0  # Spawn nepřítele každé 2 sekundy
var enemies_spawned_in_wave = 0

# Trap systém
var trap_spawn_timer = 0.0
var trap_spawn_interval = 8.0  # Spawn pasti každých 8 sekund
var max_traps = 5
var wave_transition_timer = 0.0
var wave_transition_delay = 3.0

func _ready():
	print("🎮 Herní scéna spuštěna")
	game_manager.reset_game()

	# Spawn hráče
	spawn_player()

	# Začít první vlnu
	start_new_wave()

func _input(event):
	# Otevření shopu klávesou P
	if event.is_action_pressed("shop"):
		print("🛒 Otevírání shopu...")
		get_tree().change_scene_to_file("res://scenes/shop.tscn")

func _process(delta):
	# UI PODLE PYTHON VERZE - PŘESNÉ FORMÁTOVÁNÍ

	# SKÓRE (střed nahoře)
	if score_label:
		score_label.text = "Score: " + str(game_manager.score)

	# HP BAR (levý horní roh s pozadím)
	if health_label:
		health_label.text = str(game_manager.player_health) + "/" + str(game_manager.PLAYER_MAX_HEALTH)

	if hp_bar:
		# Aktualizace HP baru podle zdraví
		var health_ratio = float(game_manager.player_health) / float(game_manager.PLAYER_MAX_HEALTH)
		var max_width = 200.0  # Šířka HP baru
		var current_width = max_width * health_ratio
		hp_bar.size.x = current_width

		# Barva podle zdraví
		if health_ratio > 0.6:
			hp_bar.color = Color.GREEN
		elif health_ratio > 0.3:
			hp_bar.color = Color.YELLOW
		else:
			hp_bar.color = Color.RED

	# SVĚTLO (pravý horní roh s pozadím)
	if light_label:
		light_label.text = "Light: " + str(int(game_manager.light_radius))

	# WAVE INFO (střed)
	if wave_label:
		var enemies_alive = game_manager.enemies_in_wave - game_manager.enemies_killed_in_wave
		wave_label.text = "Wave: " + str(game_manager.current_wave) + " | Enemies: " + str(enemies_alive)

	# WEAPON INFO (střed)
	if weapon_label:
		var weapon_data = game_manager.get_current_weapon_data()
		weapon_label.text = "Weapon: " + weapon_data["name"]

	# WEAPON KEYS (střed)
	if weapon_keys_label:
		var keys_text = ""
		if GameManager.WeaponType.PISTOL in game_manager.owned_weapons:
			keys_text += "1:Pistol "
		if GameManager.WeaponType.SHOTGUN in game_manager.owned_weapons:
			keys_text += "2:Shotgun "
		if GameManager.WeaponType.MACHINEGUN in game_manager.owned_weapons:
			keys_text += "3:MachineGun"
		weapon_keys_label.text = keys_text

	# STATUS EFEKTY (střed)
	if status_label:
		var status_text = ""
		if game_manager.player_poisoned:
			status_text += "POISONED (" + str(int(game_manager.poison_timer)) + "s) "
		if game_manager.player_slowed:
			status_text += "SLOWED (" + str(int(game_manager.slow_timer)) + "s) "
		status_label.text = status_text

	# Wave systém
	if game_manager.wave_active:
		# Spawn nepřátel pro aktuální vlnu
		enemy_spawn_timer += delta
		if enemy_spawn_timer >= enemy_spawn_interval and enemies_spawned_in_wave < game_manager.enemies_in_wave:
			enemy_spawn_timer = 0.0
			spawn_enemy_for_wave()
	else:
		# Čekání na další vlnu
		wave_transition_timer += delta
		if wave_transition_timer >= wave_transition_delay:
			wave_transition_timer = 0.0
			start_new_wave()

	# Trap systém
	trap_spawn_timer += delta
	if trap_spawn_timer >= trap_spawn_interval:
		spawn_random_trap()
		trap_spawn_timer = 0.0

	# Kontrola stavu hry
	if game_manager.current_state == GameManager.GameState.GAME_OVER:
		print("🎮 Hra skončila!")
		# Zde by se měla načíst Game Over scéna

func spawn_player():
	print("👤 Spawn hráče")
	var player_scene = preload("res://scenes/player.tscn")
	var player = player_scene.instantiate()
	add_child(player)

func start_new_wave():
	var next_wave = game_manager.current_wave + 1
	print("🌊 Začíná vlna ", next_wave)

	game_manager.start_wave(next_wave)
	enemies_spawned_in_wave = 0

func spawn_enemy_for_wave():
	print("👹 Spawn nepřítele pro vlnu ", game_manager.current_wave)
	var enemy_scene = preload("res://scenes/enemy.tscn")
	var enemy = enemy_scene.instantiate()

	# Náhodná pozice na okraji obrazovky
	var spawn_side = randi() % 4
	var spawn_pos = Vector2.ZERO

	match spawn_side:
		0:  # Horní strana
			spawn_pos = Vector2(randf() * game_manager.SCREEN_WIDTH, -50)
		1:  # Pravá strana
			spawn_pos = Vector2(game_manager.SCREEN_WIDTH + 50, randf() * game_manager.SCREEN_HEIGHT)
		2:  # Dolní strana
			spawn_pos = Vector2(randf() * game_manager.SCREEN_WIDTH, game_manager.SCREEN_HEIGHT + 50)
		3:  # Levá strana
			spawn_pos = Vector2(-50, randf() * game_manager.SCREEN_HEIGHT)

	enemy.position = spawn_pos
	add_child(enemy)

	# Nastavení statistik podle vlny - po přidání do scény
	enemy.call_deferred("setup_for_wave", game_manager.current_wave)

	enemies_spawned_in_wave += 1

func spawn_random_trap():
	# Kontrola maximálního počtu pastí
	var current_traps = get_tree().get_nodes_in_group("trap")
	if current_traps.size() >= max_traps:
		return

	print("🕳️ Spawn pasti")
	var trap_scene = preload("res://scenes/trap.tscn")
	var trap = trap_scene.instantiate()

	# Náhodná pozice na obrazovce (ne příliš blízko okrajů)
	var margin = 50
	var spawn_pos = Vector2(
		randf_range(margin, game_manager.SCREEN_WIDTH - margin),
		randf_range(margin, game_manager.SCREEN_HEIGHT - margin)
	)

	trap.position = spawn_pos
	add_child(trap)

	# Náhodný typ pasti
	var trap_types = [
		GameManager.TrapType.POISON,
		GameManager.TrapType.SLOW,
		GameManager.TrapType.SPIKE
	]
	var random_type = trap_types[randi() % trap_types.size()]
	trap.call_deferred("setup_trap", random_type)
