extends Node2D

@onready var game_manager = GameManager
@onready var ui_layer = $UILayer
@onready var score_label = $UILayer/ScoreLabel
@onready var health_label = $UILayer/HealthLabel
@onready var light_label = $UILayer/LightLabel
@onready var wave_label = $UILayer/WaveLabel
@onready var weapon_label = $UILayer/WeaponLabel

var enemy_spawn_timer = 0.0
var enemy_spawn_interval = 2.0  # Spawn nepřítele každé 2 sekundy
var enemies_spawned_in_wave = 0
var wave_transition_timer = 0.0
var wave_transition_delay = 3.0

func _ready():
	print("🎮 Herní scéna spuštěna")
	game_manager.reset_game()

	# Spawn hráče
	spawn_player()

	# Začít první vlnu
	start_new_wave()

func _process(delta):
	# Update UI
	if score_label:
		score_label.text = "Skóre: " + str(game_manager.score)

	if health_label:
		var player = get_tree().get_first_node_in_group("player")
		if player:
			health_label.text = "Zdraví: " + str(player.health) + "/" + str(player.max_health)
		else:
			health_label.text = "Zdraví: 0/100"

	if light_label:
		var light_percentage = game_manager.get_light_percentage() * 100
		light_label.text = "Světlo: " + str(int(game_manager.light_radius)) + " (" + str(int(light_percentage)) + "%)"

	if wave_label:
		if game_manager.wave_active:
			wave_label.text = "Vlna " + str(game_manager.current_wave) + " (" + str(game_manager.enemies_killed_in_wave) + "/" + str(game_manager.enemies_in_wave) + ")"
		else:
			wave_label.text = "Příprava další vlny..."

	if weapon_label:
		var weapon_data = game_manager.get_current_weapon_data()
		weapon_label.text = "Zbraň: " + weapon_data["name"] + " (" + str(game_manager.owned_weapons.size()) + " vlastněno)"

	# Wave systém
	if game_manager.wave_active:
		# Spawn nepřátel pro aktuální vlnu
		enemy_spawn_timer += delta
		if enemy_spawn_timer >= enemy_spawn_interval and enemies_spawned_in_wave < game_manager.enemies_in_wave:
			enemy_spawn_timer = 0.0
			spawn_enemy_for_wave()
	else:
		# Čekání na další vlnu
		wave_transition_timer += delta
		if wave_transition_timer >= wave_transition_delay:
			wave_transition_timer = 0.0
			start_new_wave()

	# Kontrola stavu hry
	if game_manager.current_state == GameManager.GameState.GAME_OVER:
		print("🎮 Hra skončila!")
		# Zde by se měla načíst Game Over scéna

func spawn_player():
	print("👤 Spawn hráče")
	var player_scene = preload("res://scenes/player.tscn")
	var player = player_scene.instantiate()
	add_child(player)

func start_new_wave():
	var next_wave = game_manager.current_wave + 1
	print("🌊 Začíná vlna ", next_wave)

	game_manager.start_wave(next_wave)
	enemies_spawned_in_wave = 0

func spawn_enemy_for_wave():
	print("👹 Spawn nepřítele pro vlnu ", game_manager.current_wave)
	var enemy_scene = preload("res://scenes/enemy.tscn")
	var enemy = enemy_scene.instantiate()

	# Náhodná pozice na okraji obrazovky
	var spawn_side = randi() % 4
	var spawn_pos = Vector2.ZERO

	match spawn_side:
		0:  # Horní strana
			spawn_pos = Vector2(randf() * game_manager.SCREEN_WIDTH, -50)
		1:  # Pravá strana
			spawn_pos = Vector2(game_manager.SCREEN_WIDTH + 50, randf() * game_manager.SCREEN_HEIGHT)
		2:  # Dolní strana
			spawn_pos = Vector2(randf() * game_manager.SCREEN_WIDTH, game_manager.SCREEN_HEIGHT + 50)
		3:  # Levá strana
			spawn_pos = Vector2(-50, randf() * game_manager.SCREEN_HEIGHT)

	enemy.position = spawn_pos
	add_child(enemy)

	# Nastavení statistik podle vlny - po přidání do scény
	enemy.call_deferred("setup_for_wave", game_manager.current_wave)

	enemies_spawned_in_wave += 1
