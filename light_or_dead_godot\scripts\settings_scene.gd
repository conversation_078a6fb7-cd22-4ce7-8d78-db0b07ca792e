extends Control

@onready var game_manager = get_node("/root/GameManager")
@onready var tab_container = $TabContainer
@onready var back_button = $BackButton

# Graphics tab controls
@onready var resolution_option = $TabContainer/Graphics/ResolutionOption
@onready var quality_option = $TabContainer/Graphics/QualityOption
@onready var vsync_check = $TabContainer/Graphics/VSyncCheck
@onready var fps_check = $TabContainer/Graphics/FPSCheck

# Audio tab controls
@onready var master_volume_slider = $TabContainer/Audio/MasterVolumeSlider
@onready var music_volume_slider = $TabContainer/Audio/MusicVolumeSlider

# Gameplay tab controls
@onready var difficulty_option = $TabContainer/Gameplay/DifficultyOption

func _ready():
	# Connect back button
	back_button.connect("pressed", Callable(self, "_on_BackButton_pressed"))
	
	# Set up resolution options
	var resolutions = ["800x600", "1024x768", "1280x720", "1920x1080"]
	for res in resolutions:
		resolution_option.add_item(res)
	
	# Set up quality options
	var qualities = ["Low", "Medium", "High"]
	for quality in qualities:
		quality_option.add_item(quality)
	
	# Set up difficulty options
	var difficulties = ["Easy", "Normal", "Hard"]
	for difficulty in difficulties:
		difficulty_option.add_item(difficulty)
	
	# Load current settings
	load_settings()
	
	# Connect signals
	connect_signals()

func load_settings():
	# Graphics settings
	var resolution_index = 0
	match Vector2i(game_manager.settings["resolution"]):
		Vector2i(800, 600): resolution_index = 0
		Vector2i(1024, 768): resolution_index = 1
		Vector2i(1280, 720): resolution_index = 2
		Vector2i(1920, 1080): resolution_index = 3
	resolution_option.selected = resolution_index
	
	quality_option.selected = game_manager.settings["quality"]
	vsync_check.button_pressed = game_manager.settings["vsync"]
	fps_check.button_pressed = game_manager.settings["show_fps"]
	
	# Audio settings
	master_volume_slider.value = game_manager.settings["volume"]
	music_volume_slider.value = game_manager.settings["music_volume"]
	
	# Gameplay settings
	difficulty_option.selected = game_manager.settings["difficulty"]

func connect_signals():
	# Graphics signals
	resolution_option.connect("item_selected", Callable(self, "_on_ResolutionOption_item_selected"))
	quality_option.connect("item_selected", Callable(self, "_on_QualityOption_item_selected"))
	vsync_check.connect("toggled", Callable(self, "_on_VSyncCheck_toggled"))
	fps_check.connect("toggled", Callable(self, "_on_FPSCheck_toggled"))
	
	# Audio signals
	master_volume_slider.connect("value_changed", Callable(self, "_on_MasterVolumeSlider_value_changed"))
	music_volume_slider.connect("value_changed", Callable(self, "_on_MusicVolumeSlider_value_changed"))
	
	# Gameplay signals
	difficulty_option.connect("item_selected", Callable(self, "_on_DifficultyOption_item_selected"))

func _on_ResolutionOption_item_selected(index):
	var resolution = Vector2.ZERO
	match index:
		0: resolution = Vector2(800, 600)
		1: resolution = Vector2(1024, 768)
		2: resolution = Vector2(1280, 720)
		3: resolution = Vector2(1920, 1080)
	
	game_manager.settings["resolution"] = resolution
	apply_resolution()

func _on_QualityOption_item_selected(index):
	game_manager.settings["quality"] = index

func _on_VSyncCheck_toggled(button_pressed):
	game_manager.settings["vsync"] = button_pressed
	DisplayServer.window_set_vsync_mode(DisplayServer.VSYNC_ENABLED if button_pressed else DisplayServer.VSYNC_DISABLED)

func _on_FPSCheck_toggled(button_pressed):
	game_manager.settings["show_fps"] = button_pressed

func _on_MasterVolumeSlider_value_changed(value):
	game_manager.settings["volume"] = value
	AudioServer.set_bus_volume_db(AudioServer.get_bus_index("Master"), linear_to_db(value))

func _on_MusicVolumeSlider_value_changed(value):
	game_manager.settings["music_volume"] = value
	# Apply to music bus if you have one

func _on_DifficultyOption_item_selected(index):
	game_manager.settings["difficulty"] = index

func apply_resolution():
	var resolution = game_manager.settings["resolution"]
	get_window().size = Vector2i(resolution.x, resolution.y)

func _on_BackButton_pressed():
	# Return to the main menu
	game_manager.change_game_state(game_manager.GameState.MAIN_MENU)
	get_tree().change_scene_to_file("res://scenes/main_menu.tscn")
