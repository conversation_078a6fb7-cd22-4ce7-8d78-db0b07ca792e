from PIL import Image, ImageDraw
import os

# Vyt<PERSON><PERSON><PERSON><PERSON> ad<PERSON>e
os.makedirs("light_or_dead_godot/assets/images", exist_ok=True)

def create_circle(filename, size, color):
    """Vyt<PERSON><PERSON><PERSON> jednoduchý kruh"""
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    draw.ellipse((2, 2, size-2, size-2), fill=color)
    img.save(f"light_or_dead_godot/assets/images/{filename}")
    print(f"✅ Created {filename}")

def create_rect(filename, width, height, color):
    """Vytvoří jednoduchý obdélník"""
    img = Image.new('RGBA', (width, height), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    draw.rectangle((2, 2, width-2, height-2), fill=color)
    img.save(f"light_or_dead_godot/assets/images/{filename}")
    print(f"✅ Created {filename}")

# <PERSON><PERSON><PERSON><PERSON><PERSON> obr<PERSON><PERSON> pro testován<PERSON>
create_circle("player.png", 32, (255, 255, 255, 255))  # <PERSON><PERSON><PERSON><PERSON> kruh pro hráče
create_circle("enemy.png", 24, (255, 0, 0, 255))       # Červený kruh pro nepřítele
create_circle("bullet.png", 8, (255, 255, 0, 255))     # Žlutý kruh pro střelu

# Zbraně
create_rect("weapon_pistol.png", 30, 15, (200, 200, 0, 255))      # Žlutý obdélník
create_rect("weapon_shotgun.png", 40, 20, (200, 100, 0, 255))     # Oranžový obdélník
create_rect("weapon_machinegun.png", 50, 12, (0, 200, 200, 255))  # Tyrkysový obdélník

# Pasti
create_circle("trap_poison.png", 20, (0, 255, 0, 255))    # Zelený kruh
create_circle("trap_slow.png", 20, (0, 0, 255, 255))      # Modrý kruh
create_circle("trap_spike.png", 20, (255, 0, 0, 255))     # Červený kruh

print("✅ Všechny základní obrázky vytvořeny!")
