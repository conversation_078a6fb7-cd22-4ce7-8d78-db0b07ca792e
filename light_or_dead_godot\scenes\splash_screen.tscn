[gd_scene load_steps=4 format=3 uid="uid://bvn8yxkqjyxe"]

[ext_resource type="Script" uid="uid://bed8biga2pasd" path="res://scripts/splash_screen.gd" id="1_splash"]

[sub_resource type="Animation" id="Animation_1"]
resource_name = "fade_in"
length = 2.0
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("VBoxContainer/LogoLabel:modulate:a")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 1.0),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [0.0, 1.0]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("VBoxContainer/SubtitleLabel:modulate:a")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0.5, 1.5),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [0.0, 1.0]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_1"]
_data = {
"fade_in": SubResource("Animation_1")
}

[node name="SplashScreen" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
color = Color(0, 0, 0, 1)
script = ExtResource("1_splash")

[node name="Background" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
color = Color(0.05, 0.05, 0.1, 1)

[node name="VBoxContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -200.0
offset_top = -100.0
offset_right = 200.0
offset_bottom = 100.0
alignment = 1

[node name="LogoLabel" type="Label" parent="VBoxContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 48
text = "LIGHT OR DEAD"
horizontal_alignment = 1
vertical_alignment = 1

[node name="SubtitleLabel" type="Label" parent="VBoxContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 20
text = "Enhanced Edition"
horizontal_alignment = 1
vertical_alignment = 1

[node name="ClickLabel" type="Label" parent="VBoxContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 16
text = "Klikněte kamkoliv pro pokračování"
horizontal_alignment = 1
vertical_alignment = 1

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
"default": SubResource("AnimationLibrary_1")
}
