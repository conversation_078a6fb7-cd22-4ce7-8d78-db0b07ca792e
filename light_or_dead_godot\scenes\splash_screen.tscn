[gd_scene load_steps=5 format=3]

[ext_resource type="Script" path="res://scripts/splash_screen.gd" id="1"]

[sub_resource type="Animation" id="1"]
resource_name = "fade_in"
length = 2.0
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath(".:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 2),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Color(1, 1, 1, 0), Color(1, 1, 1, 1)]
}

[sub_resource type="Animation" id="2"]
resource_name = "fade_out"
length = 1.0
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath(".:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 1),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Color(1, 1, 1, 1), Color(1, 1, 1, 0)]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_1"]
_data = {
"fade_in": SubResource("1"),
"fade_out": SubResource("2")
}

[node name="SplashScreen" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1")

[node name="ColorRect" type="ColorRect" parent="."]
layout_mode = 0
anchor_right = 1.0
anchor_bottom = 1.0
color = Color(0, 0, 0, 1)

[node name="LogoLabel" type="Label" parent="."]
layout_mode = 0
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -200.0
offset_top = -50.0
offset_right = 200.0
offset_bottom = 0.0
theme_override_colors/font_color = Color(1, 0.862745, 0.588235, 1)
theme_override_font_sizes/font_size = 48
text = "LIGHT OR DEAD"
horizontal_alignment = 1

[node name="SubtitleLabel" type="Label" parent="."]
layout_mode = 0
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -200.0
offset_top = 10.0
offset_right = 200.0
offset_bottom = 40.0
theme_override_font_sizes/font_size = 20
text = "Survive the Darkness"
horizontal_alignment = 1

[node name="ClickLabel" type="Label" parent="."]
layout_mode = 0
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -100.0
offset_top = -100.0
offset_right = 100.0
offset_bottom = -70.0
theme_override_font_sizes/font_size = 16
text = "Click to continue..."
horizontal_alignment = 1

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
"": SubResource("AnimationLibrary_1")
}

[node name="Timer" type="Timer" parent="."]
wait_time = 5.0
one_shot = true
autostart = true
