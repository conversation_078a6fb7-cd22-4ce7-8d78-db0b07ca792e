[gd_scene load_steps=4 format=3 uid="uid://bullet_scene"]

[ext_resource type="Script" path="res://scripts/bullet.gd" id="1_bullet"]
[ext_resource type="Texture2D" uid="uid://bullet_texture" path="res://assets/images/bullet.png" id="2_bullet"]

[sub_resource type="CircleShape2D" id="CircleShape2D_1"]
radius = 4.0

[node name="Bullet" type="Area2D" groups=["bullets"]]
script = ExtResource("1_bullet")

[node name="Sprite2D" type="Sprite2D" parent="."]
texture = ExtResource("2_bullet")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("CircleShape2D_1")
