extends Control

@onready var score_label = $TopBar/ScoreLabel
@onready var back_button = $TopBar/BackButton
@onready var items_container = $ScrollContainer/ItemsContainer

var item_buttons = []

func _ready():
	print("🛒 Shop otevřen")

	# <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> signálů
	back_button.pressed.connect(_on_back_pressed)

	# Vytvoření položek shopu
	create_shop_items()

	# Aktualizace UI
	update_ui()

func create_shop_items():
	# Vy<PERSON>ištění kontejneru
	for child in items_container.get_children():
		child.queue_free()

	item_buttons.clear()

	# Vytvoření tlačítek pro každou položku
	for item in GameManager.shop_items:
		var item_panel = create_item_panel(item)
		items_container.add_child(item_panel)

func create_item_panel(item: Dictionary) -> Control:
	var panel = Panel.new()
	panel.custom_minimum_size = Vector2(350, 100)

	var vbox = VBoxContainer.new()
	panel.add_child(vbox)
	vbox.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	vbox.add_theme_constant_override("separation", 5)

	# <PERSON><PERSON><PERSON><PERSON>
	var name_label = Label.new()
	name_label.text = item["name"]
	name_label.add_theme_font_size_override("font_size", 20)
	vbox.add_child(name_label)

	# Popis
	var desc_label = Label.new()
	desc_label.text = item["description"]
	desc_label.add_theme_font_size_override("font_size", 14)
	vbox.add_child(desc_label)

	# Spodní řádek s cenou a tlačítkem
	var hbox = HBoxContainer.new()
	vbox.add_child(hbox)

	# Cena
	var cost_label = Label.new()
	cost_label.text = "Cena: " + str(item["cost"]) + " bodů"
	cost_label.add_theme_font_size_override("font_size", 16)
	hbox.add_child(cost_label)

	# Spacer
	var spacer = Control.new()
	spacer.size_flags_horizontal = Control.SIZE_EXPAND_FILL
	hbox.add_child(spacer)

	# Tlačítko koupit
	var buy_button = Button.new()
	buy_button.text = "Koupit"
	buy_button.custom_minimum_size = Vector2(80, 30)

	# Připojení signálu
	var item_id = item["id"]
	buy_button.pressed.connect(_on_buy_item.bind(item_id))

	hbox.add_child(buy_button)

	# Uložení reference na tlačítko
	item_buttons.append({
		"id": item_id,
		"button": buy_button,
		"cost_label": cost_label
	})

	return panel

func update_ui():
	# Aktualizace skóre
	score_label.text = "Skóre: " + str(GameManager.score)

	# Aktualizace dostupnosti tlačítek
	for item_data in item_buttons:
		var can_buy = GameManager.can_buy_item(item_data["id"])
		item_data["button"].disabled = not can_buy

		# Změna barvy podle dostupnosti
		if can_buy:
			item_data["button"].modulate = Color.WHITE
			item_data["cost_label"].modulate = Color.WHITE
		else:
			item_data["button"].modulate = Color.GRAY
			item_data["cost_label"].modulate = Color.GRAY

func _on_buy_item(item_id: String):
	print("🛒 Pokus o nákup: ", item_id)

	if GameManager.buy_shop_item(item_id):
		print("✅ Nákup úspěšný!")
		update_ui()
	else:
		print("❌ Nákup neúspěšný!")

func _on_back_pressed():
	print("🔙 Návrat")
	# Pokud jsme ve hře, vraťme se do hry, jinak do menu
	if GameManager.current_state == GameManager.GameState.PLAYING:
		get_tree().change_scene_to_file("res://scenes/game_scene.tscn")
	else:
		get_tree().change_scene_to_file("res://scenes/main_menu.tscn")
