[gd_scene load_steps=3 format=3]

[ext_resource type="Script" path="res://scripts/enemy.gd" id="1"]

[sub_resource type="CircleShape2D" id="1"]
radius = 25.0

[node name="Boss" type="CharacterBody2D" groups=["bosses", "enemies"]]
script = ExtResource("1")
is_boss = true

[node name="Sprite2D" type="Sprite2D" parent="."]
modulate = Color(1, 0, 1, 1)
scale = Vector2(1, 1)

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("1")

[node name="HealthBar" type="ProgressBar" parent="."]
modulate = Color(1, 0, 1, 0.7)
offset_left = -30.0
offset_top = -40.0
offset_right = 30.0
offset_bottom = -35.0
max_value = 50.0
value = 50.0
show_percentage = false

[node name="ShootTimer" type="Timer" parent="."]
wait_time = 3.0
one_shot = true

[node name="ActionTimer" type="Timer" parent="."]
wait_time = 3.0
one_shot = true
autostart = true
