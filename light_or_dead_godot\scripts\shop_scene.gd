extends Control

@onready var game_manager = get_node("/root/GameManager")
@onready var score_label = $ScoreLabel
@onready var back_button = $BackButton
@onready var item_grid = $ItemGrid

var shop_items = []
var item_buttons = []

func _ready():
	# Connect back button
	back_button.connect("pressed", Callable(self, "_on_BackButton_pressed"))
	
	# Update score display
	score_label.text = "Score: " + str(game_manager.score)
	
	# Load shop items
	shop_items = game_manager.shop_items
	
	# Create shop item buttons
	create_shop_items()

func _process(delta):
	# Keep score updated
	score_label.text = "Score: " + str(game_manager.score)
	
	# Update item availability based on score
	update_item_availability()

func create_shop_items():
	# Clear any existing items
	for child in item_grid.get_children():
		child.queue_free()
	
	item_buttons = []
	
	# Create a button for each shop item
	for item in shop_items:
		var item_button = preload("res://scenes/shop_item.tscn").instantiate()
		item_grid.add_child(item_button)
		
		# Set up the item button
		item_button.item_id = item["id"]
		item_button.item_name = item["name"]
		item_button.item_cost = item["cost"]
		item_button.item_description = item["desc"]
		
		# Check if it's a weapon that's already owned
		if item["id"] == "shotgun" and game_manager.weapons[game_manager.WeaponType.SHOTGUN]["owned"]:
			item_button.owned = true
		elif item["id"] == "machinegun" and game_manager.weapons[game_manager.WeaponType.MACHINEGUN]["owned"]:
			item_button.owned = true
		else:
			item_button.owned = false
		
		# Connect the button's pressed signal
		item_button.connect("item_purchased", Callable(self, "_on_item_purchased"))
		
		item_buttons.append(item_button)
	
	# Initial update of availability
	update_item_availability()

func update_item_availability():
	for button in item_buttons:
		# Check if player can afford the item
		button.can_afford = game_manager.score >= button.item_cost
		
		# Update the button's appearance
		button.update_appearance()

func _on_item_purchased(item_id):
	# Find the item in the shop items
	for item in shop_items:
		if item["id"] == item_id:
			# Check if player can afford it
			if game_manager.score >= item["cost"]:
				# Purchase the item
				game_manager.buy_item(item_id)
				
				# Update the UI
				score_label.text = "Score: " + str(game_manager.score)
				update_item_availability()
				
				# Play purchase sound
				# $PurchaseSound.play()
				
				# Show purchase effect
				var item_button = find_item_button(item_id)
				if item_button:
					item_button.play_purchase_effect()
				
				break

func find_item_button(item_id):
	for button in item_buttons:
		if button.item_id == item_id:
			return button
	return null

func _on_BackButton_pressed():
	# Return to the game
	game_manager.change_game_state(game_manager.GameState.PLAYING)
	get_tree().change_scene_to_file("res://scenes/game_scene.tscn")
