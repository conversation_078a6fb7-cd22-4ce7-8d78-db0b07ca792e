[gd_scene load_steps=5 format=3]

[ext_resource type="Script" path="res://scripts/main_menu.gd" id="1"]

[sub_resource type="Animation" id="1"]
resource_name = "background_animation"
length = 10.0
loop_mode = 1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Background:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 5, 10),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Color(0.05, 0.05, 0.1, 1), Color(0.1, 0.05, 0.15, 1), Color(0.05, 0.05, 0.1, 1)]
}

[sub_resource type="Animation" id="2"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Background:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Color(0.05, 0.05, 0.1, 1)]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_1"]
_data = {
"RESET": SubResource("2"),
"background_animation": SubResource("1")
}

[node name="MainMenu" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1")

[node name="Background" type="ColorRect" parent="."]
modulate = Color(0.05, 0.05, 0.1, 1)
layout_mode = 0
anchor_right = 1.0
anchor_bottom = 1.0
color = Color(0, 0, 0, 1)

[node name="TitleLabel" type="Label" parent="."]
layout_mode = 0
anchor_left = 0.5
anchor_top = 0.2
anchor_right = 0.5
anchor_bottom = 0.2
offset_left = -200.0
offset_top = -30.0
offset_right = 200.0
offset_bottom = 30.0
theme_override_colors/font_color = Color(1, 0.862745, 0.588235, 1)
theme_override_font_sizes/font_size = 48
text = "LIGHT OR DEAD"
horizontal_alignment = 1

[node name="VBoxContainer" type="VBoxContainer" parent="."]
layout_mode = 0
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -125.0
offset_top = -75.0
offset_right = 125.0
offset_bottom = 125.0
theme_override_constants/separation = 20

[node name="StartButton" type="Button" parent="VBoxContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 24
text = "Start Game"

[node name="SettingsButton" type="Button" parent="VBoxContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 24
text = "Settings"

[node name="QuitButton" type="Button" parent="VBoxContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 24
text = "Quit"

[node name="HighScoreLabel" type="Label" parent="."]
layout_mode = 0
anchor_left = 0.5
anchor_top = 0.9
anchor_right = 0.5
anchor_bottom = 0.9
offset_left = -100.0
offset_top = -15.0
offset_right = 100.0
offset_bottom = 15.0
text = "High Score: 0"
horizontal_alignment = 1

[node name="VersionLabel" type="Label" parent="."]
layout_mode = 0
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -100.0
offset_top = -30.0
offset_right = -10.0
offset_bottom = -10.0
text = "v1.0.0"
horizontal_alignment = 2

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
"": SubResource("AnimationLibrary_1")
}
