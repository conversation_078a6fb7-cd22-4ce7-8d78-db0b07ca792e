# Test skript pro ově<PERSON><PERSON><PERSON> funkčnosti projektu
extends Node

func _ready():
	print("🧪 TESTOVÁNÍ PROJEKTU")
	print("=" * 50)
	
	# Test GameManageru
	test_game_manager()
	
	# Test konstant
	test_constants()
	
	# Test zbraní
	test_weapons()
	
	print("=" * 50)
	print("✅ VŠECHNY TESTY DOKONČENY")

func test_game_manager():
	print("🔍 Test GameManageru...")
	
	var gm = GameManager
	
	# Test základních vlastností
	assert(gm.SCREEN_WIDTH == 800, "<PERSON><PERSON>ř<PERSON> obrazovky")
	assert(gm.SCREEN_HEIGHT == 600, "Výš<PERSON> obrazovky")
	assert(gm.PLAYER_MAX_HEALTH == 100, "Max zdraví hráče")
	
	# Test světelného systému
	assert(gm.INITIAL_LIGHT_RADIUS == 180.0, "<PERSON>čátečn<PERSON> světlo")
	assert(gm.MAX_LIGHT_RADIUS == 450.0, "Max světlo")
	assert(gm.MIN_LIGHT_RADIUS == 35.0, "Min světlo")
	
	print("   ✅ GameManager OK")

func test_constants():
	print("🔍 Test konstant...")
	
	var gm = GameManager
	
	# Test herních stavů
	assert(gm.GameState.MENU == 0, "Stav MENU")
	assert(gm.GameState.PLAYING == 1, "Stav PLAYING")
	assert(gm.GameState.PAUSED == 2, "Stav PAUSED")
	assert(gm.GameState.GAME_OVER == 3, "Stav GAME_OVER")
	
	print("   ✅ Konstanty OK")

func test_weapons():
	print("🔍 Test zbraní...")
	
	var gm = GameManager
	
	# Test typů zbraní
	assert(gm.WeaponType.PISTOL == 0, "Typ PISTOL")
	assert(gm.WeaponType.SHOTGUN == 1, "Typ SHOTGUN")
	assert(gm.WeaponType.MACHINEGUN == 2, "Typ MACHINEGUN")
	
	# Test dat zbraní
	var pistol_data = gm.WEAPON_DATA[gm.WeaponType.PISTOL]
	assert(pistol_data["name"] == "Pistol", "Název pistole")
	assert(pistol_data["damage"] == 10, "Poškození pistole")
	assert(pistol_data["cost"] == 0, "Cena pistole")
	
	var shotgun_data = gm.WEAPON_DATA[gm.WeaponType.SHOTGUN]
	assert(shotgun_data["bullet_count"] == 5, "Počet střel shotgun")
	assert(shotgun_data["cost"] == 300, "Cena shotgun")
	
	print("   ✅ Zbraně OK")

func assert(condition: bool, message: String):
	if not condition:
		print("   ❌ CHYBA: ", message)
		push_error("Test selhal: " + message)
	else:
		print("   ✅ ", message)
