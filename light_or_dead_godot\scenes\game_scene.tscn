[gd_scene load_steps=3 format=3]

[ext_resource type="Script" path="res://scripts/game_scene.gd" id="1"]
[ext_resource type="PackedScene" path="res://scenes/player.tscn" id="2"]

[node name="GameScene" type="Node2D"]
script = ExtResource("1")

[node name="Background" type="ColorRect" parent="."]
offset_right = 800.0
offset_bottom = 600.0
color = Color(0.05, 0.05, 0.1, 1)

[node name="Player" parent="." instance=ExtResource("2")]
position = Vector2(400, 300)

[node name="UI" type="CanvasLayer" parent="."]

[node name="ScoreLabel" type="Label" parent="UI"]
offset_left = 20.0
offset_top = 20.0
offset_right = 200.0
offset_bottom = 50.0
text = "Score: 0"

[node name="WaveLabel" type="Label" parent="UI"]
visible = false
anchor_left = 0.5
anchor_right = 0.5
offset_left = -100.0
offset_top = 50.0
offset_right = 100.0
offset_bottom = 80.0
theme_override_font_sizes/font_size = 24
text = "WAVE 1"
horizontal_alignment = 1

[node name="HealthBar" type="ProgressBar" parent="UI"]
anchor_left = 1.0
anchor_right = 1.0
offset_left = -220.0
offset_top = 20.0
offset_right = -20.0
offset_bottom = 40.0
theme_override_colors/font_color = Color(1, 1, 1, 1)
max_value = 100.0
value = 100.0

[node name="HealthLabel" type="Label" parent="UI/HealthBar"]
layout_mode = 0
anchor_right = 1.0
anchor_bottom = 1.0
text = "Health"
horizontal_alignment = 1
vertical_alignment = 1

[node name="LightBar" type="ProgressBar" parent="UI"]
anchor_left = 1.0
anchor_right = 1.0
offset_left = -220.0
offset_top = 50.0
offset_right = -20.0
offset_bottom = 70.0
theme_override_colors/font_color = Color(1, 1, 1, 1)
min_value = 35.0
max_value = 450.0
value = 180.0

[node name="LightLabel" type="Label" parent="UI/LightBar"]
layout_mode = 0
anchor_right = 1.0
anchor_bottom = 1.0
text = "Light"
horizontal_alignment = 1
vertical_alignment = 1

[node name="WeaponLabel" type="Label" parent="UI"]
offset_left = 20.0
offset_top = 560.0
offset_right = 200.0
offset_bottom = 590.0
text = "Weapon: Pistol"

[node name="WaveTimer" type="Timer" parent="."]
wait_time = 4.0
one_shot = true

[node name="SpawnTimer" type="Timer" parent="."]
wait_time = 0.5
one_shot = true

[node name="WaveMessageTimer" type="Timer" parent="."]
wait_time = 2.5
one_shot = true
