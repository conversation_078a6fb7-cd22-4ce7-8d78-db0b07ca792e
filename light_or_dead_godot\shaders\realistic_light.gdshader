shader_type canvas_item;

// Uniformy pro světelný systém
uniform vec2 light_position : hint_default(400.0, 300.0);
uniform float light_radius : hint_range(0.0, 500.0) = 180.0;
uniform vec3 light_color : source_color = vec3(1.0, 0.863, 0.588); // (255, 220, 150) / 255
uniform vec3 ambient_color : source_color = vec3(0.03, 0.03, 0.08);
uniform float time : hint_default(0.0);
uniform vec2 screen_size : hint_default(800.0, 600.0);

// Perlin noise funkce pro realistické plápolání
float random(vec2 st) {
    return fract(sin(dot(st.xy + time * 0.001, vec2(12.9898, 78.233))) * 43758.5453123);
}

float noise(vec2 st) {
    vec2 i = floor(st);
    vec2 f = fract(st);
    vec2 t = vec2(time * 0.02, time * 0.01);
    
    float a = random(i + t);
    float b = random(i + vec2(1.0, 0.0) + t);
    float c = random(i + vec2(0.0, 1.0) + t);
    float d = random(i + vec2(1.0, 1.0) + t);
    
    vec2 u = f * f * (3.0 - 2.0 * f);
    return mix(mix(a, b, u.x), mix(c, d, u.x), u.y);
}

void fragment() {
    // Získání pozice pixelu v screen space
    vec2 pixel_coord = SCREEN_UV * screen_size;
    float distance = length(pixel_coord - light_position);
    
    // Jemnější přechod světla s větším vnitřním kruhem (jako v Python verzi)
    float inner_radius = light_radius * 0.4;  // Větší vnitřní kruh plného světla
    float outer_radius = light_radius;
    
    // Jemné blikání pomocí Perlin noise - efekt plápolání svíčky
    float flicker = noise(vec2(time * 0.5, time * 0.7)) * 0.1 + 0.95;
    
    // Aplikace blikání na vnitřní i vnější poloměr
    inner_radius *= flicker;
    outer_radius *= flicker;
    
    // Jemnější přechod mezi světlem a tmou
    float light_intensity = 1.0;
    if (distance > inner_radius) {
        light_intensity = 1.0 - smoothstep(inner_radius, outer_radius, distance);
        light_intensity = pow(light_intensity, 1.5); // Jemnější přechod
    }
    
    // Přidání teplého odstínu pro efekt svíčky
    vec3 warm_glow = vec3(1.0, 0.9, 0.7) * light_color;
    
    // Finální barva s jemnějším přechodem
    vec3 final_color = mix(COLOR.rgb * ambient_color, COLOR.rgb * warm_glow, light_intensity);
    
    COLOR = vec4(final_color, COLOR.a);
}
