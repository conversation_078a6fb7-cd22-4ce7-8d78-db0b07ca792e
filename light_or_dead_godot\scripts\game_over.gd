extends Control

@onready var game_manager = get_node("/root/GameManager")
@onready var score_label = $ScoreLabel
@onready var high_score_label = $HighScoreLabel
@onready var wave_label = $WaveLabel
@onready var retry_button = $RetryButton
@onready var main_menu_button = $MainMenuButton
@onready var animation_player = $AnimationPlayer

func _ready():
	# Connect buttons
	retry_button.connect("pressed", Callable(self, "_on_RetryButton_pressed"))
	main_menu_button.connect("pressed", Callable(self, "_on_MainMenuButton_pressed"))
	
	# Update score and wave display
	score_label.text = "Score: " + str(game_manager.score)
	high_score_label.text = "High Score: " + str(game_manager.high_score)
	wave_label.text = "Wave: " + str(game_manager.current_wave)
	
	# Play fade-in animation
	animation_player.play("fade_in")

func _on_RetryButton_pressed():
	# Reset game and start a new one
	game_manager.reset_game()
	game_manager.change_game_state(game_manager.GameState.PLAYING)
	get_tree().change_scene_to_file("res://scenes/game_scene.tscn")

func _on_MainMenuButton_pressed():
	# Return to main menu
	game_manager.change_game_state(game_manager.GameState.MAIN_MENU)
	get_tree().change_scene_to_file("res://scenes/main_menu.tscn")
