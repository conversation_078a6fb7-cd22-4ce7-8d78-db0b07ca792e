extends CharacterBody2D

@onready var game_manager = get_node("/root/GameManager")
@onready var sprite = $Sprite2D
@onready var light = $PointLight2D
@onready var weapon_sprite = $WeaponSprite
@onready var muzzle_position = $WeaponSprite/MuzzlePosition
@onready var shoot_timer = $ShootTimer
@onready var status_effects = $StatusEffects

var last_shot_time = 0.0
var can_shoot = true

func _ready():
	position = Vector2(game_manager.PLAYER_START_X, game_manager.PLAYER_START_Y)
	update_light_radius(game_manager.player_stats["light_radius"])

func _process(delta):
	# Handle player rotation to face mouse
	look_at(get_global_mouse_position())
	
	# Update light radius
	if game_manager.player_stats["light_to_add"] > 0:
		var amount_to_add = min(game_manager.player_stats["light_to_add"], game_manager.LIGHT_GROW_INTERP_FACTOR * delta)
		game_manager.player_stats["light_radius"] += amount_to_add
		game_manager.player_stats["light_to_add"] -= amount_to_add
		update_light_radius(game_manager.player_stats["light_radius"])
	
	# Shrink light over time
	game_manager.player_stats["light_radius"] -= game_manager.LIGHT_SHRINK_RATE * delta
	
	# Add some flicker to the light
	var flicker = randf_range(-game_manager.LIGHT_FLICKER_AMOUNT, game_manager.LIGHT_FLICKER_AMOUNT)
	update_light_radius(game_manager.player_stats["light_radius"] + flicker)
	
	# Check if light is too small (game over condition)
	if game_manager.player_stats["light_radius"] <= game_manager.LIGHT_DEATH_THRESHOLD:
		die("darkness")
	
	# Handle shooting
	if Input.is_action_pressed("shoot") and can_shoot:
		shoot()
	
	# Handle status effects
	handle_status_effects(delta)

func _physics_process(delta):
	# Get movement input
	var input_dir = Vector2.ZERO
	if Input.is_action_pressed("move_up"):
		input_dir.y -= 1
	if Input.is_action_pressed("move_down"):
		input_dir.y += 1
	if Input.is_action_pressed("move_left"):
		input_dir.x -= 1
	if Input.is_action_pressed("move_right"):
		input_dir.x += 1
	
	# Normalize and apply speed
	if input_dir.length() > 0:
		input_dir = input_dir.normalized()
	
	var speed = game_manager.PLAYER_BASE_SPEED * game_manager.player_stats["speed_multiplier"]
	
	# Apply slow effect if active
	if game_manager.player_stats["slowed"]:
		speed *= game_manager.TRAP_SLOW_FACTOR
	
	velocity = input_dir * speed
	move_and_slide()
	
	# Keep player within screen bounds
	position.x = clamp(position.x, 0, game_manager.SCREEN_WIDTH)
	position.y = clamp(position.y, 0, game_manager.SCREEN_HEIGHT)

func update_light_radius(new_radius):
	# Clamp light radius to valid range
	var clamped_radius = clamp(new_radius, game_manager.MIN_LIGHT_RADIUS, game_manager.MAX_LIGHT_RADIUS)
	game_manager.player_stats["light_radius"] = clamped_radius
	
	# Update the actual light
	light.texture_scale = clamped_radius / 100.0  # Adjust based on your light texture

func shoot():
	# Get current weapon data
	var weapon_type = game_manager.player_stats["current_weapon"]
	var weapon_data = game_manager.weapons[weapon_type]
	
	# Check if we can shoot based on fire rate
	var cooldown = game_manager.BASE_BULLET_COOLDOWN / (game_manager.player_stats["fire_rate_multiplier"] * weapon_data["fire_rate"])
	
	can_shoot = false
	shoot_timer.wait_time = cooldown
	shoot_timer.start()
	
	# Create bullets based on weapon type
	var bullet_count = weapon_data["bullet_count"]
	var spread = weapon_data["spread"]
	
	for i in range(bullet_count):
		var bullet = preload("res://scenes/bullet.tscn").instantiate()
		get_parent().add_child(bullet)
		
		# Calculate spread angle
		var angle_offset = 0.0
		if bullet_count > 1:
			angle_offset = lerp(-spread, spread, float(i) / (bullet_count - 1))
		
		# Set bullet properties
		bullet.global_position = muzzle_position.global_position
		bullet.rotation = rotation + deg_to_rad(angle_offset)
		bullet.speed = weapon_data["bullet_speed"]
		bullet.damage = weapon_data["damage"] * game_manager.player_stats["bullet_damage"]
		bullet.from_player = true
	
	# Play sound effect
	# $ShootSound.play()

func handle_status_effects(delta):
	# Handle poison effect
	if game_manager.player_stats["poisoned"]:
		game_manager.player_stats["poison_timer"] -= delta
		
		# Apply poison damage every second
		if fmod(game_manager.player_stats["poison_timer"], 1.0) < delta:
			take_damage(game_manager.TRAP_POISON_DAMAGE)
		
		# Update poison visual effect
		status_effects.modulate = Color(0, 1, 0, 0.5 + 0.5 * sin(OS.get_ticks_msec() * 0.01))
		status_effects.visible = true
		
		# Check if poison has worn off
		if game_manager.player_stats["poison_timer"] <= 0:
			game_manager.player_stats["poisoned"] = false
			status_effects.visible = false
	
	# Handle slow effect
	if game_manager.player_stats["slowed"]:
		game_manager.player_stats["slow_timer"] -= delta
		
		# Update slow visual effect
		if not game_manager.player_stats["poisoned"]:  # Don't override poison effect
			status_effects.modulate = Color(0, 0, 1, 0.5 + 0.5 * sin(OS.get_ticks_msec() * 0.01))
			status_effects.visible = true
		
		# Check if slow has worn off
		if game_manager.player_stats["slow_timer"] <= 0:
			game_manager.player_stats["slowed"] = false
			if not game_manager.player_stats["poisoned"]:
				status_effects.visible = false

func take_damage(amount):
	game_manager.player_stats["health"] -= amount
	
	# Flash the player sprite to indicate damage
	sprite.modulate = Color(1, 0, 0, 1)
	var tween = create_tween()
	tween.tween_property(sprite, "modulate", Color(1, 1, 1, 1), 0.2)
	
	# Check if player is dead
	if game_manager.player_stats["health"] <= 0:
		die("health")

func die(cause):
	# Update high score if needed
	if game_manager.score > game_manager.high_score:
		game_manager.high_score = game_manager.score
		game_manager.save_highscore()
	
	# Change to game over state
	game_manager.change_game_state(game_manager.GameState.GAME_OVER)

func apply_poison():
	game_manager.player_stats["poisoned"] = true
	game_manager.player_stats["poison_timer"] = game_manager.TRAP_POISON_DURATION

func apply_slow():
	game_manager.player_stats["slowed"] = true
	game_manager.player_stats["slow_timer"] = game_manager.TRAP_SLOW_DURATION

func _on_ShootTimer_timeout():
	can_shoot = true

func switch_weapon(weapon_index):
	if weapon_index < game_manager.player_stats["weapons"].size():
		game_manager.player_stats["current_weapon"] = game_manager.player_stats["weapons"][weapon_index]
		# Update weapon sprite
		update_weapon_sprite()

func update_weapon_sprite():
	var weapon_type = game_manager.player_stats["current_weapon"]
	# Update the weapon sprite based on the current weapon
	match weapon_type:
		game_manager.WeaponType.PISTOL:
			weapon_sprite.texture = preload("res://assets/images/weapon_pistol.png")
		game_manager.WeaponType.SHOTGUN:
			weapon_sprite.texture = preload("res://assets/images/weapon_shotgun.png")
		game_manager.WeaponType.MACHINEGUN:
			weapon_sprite.texture = preload("res://assets/images/weapon_machinegun.png")
