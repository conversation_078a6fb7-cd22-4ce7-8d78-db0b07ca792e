extends CharacterBody2D

@onready var game_manager = GameManager
@onready var sprite = $Sprite2D

var health: int
var max_health: int

func _ready():
	print("👤 Player inicializován")
	
	# Nastavení zdraví
	max_health = game_manager.PLAYER_MAX_HEALTH
	health = max_health
	
	# Nastavení pozice na střed obrazovky
	position = Vector2(game_manager.SCREEN_WIDTH / 2.0, game_manager.SCREEN_HEIGHT / 2.0)
	
	print("   Pozice: ", position)
	print("   Zdraví: ", health, "/", max_health)

func _physics_process(delta):
	# <PERSON>ískán<PERSON> vstupu
	var input_dir = Vector2.ZERO
	
	if Input.is_action_pressed("move_up"):
		input_dir.y -= 1
	if Input.is_action_pressed("move_down"):
		input_dir.y += 1
	if Input.is_action_pressed("move_left"):
		input_dir.x -= 1
	if Input.is_action_pressed("move_right"):
		input_dir.x += 1
	
	# Normalizace směru
	if input_dir.length() > 0:
		input_dir = input_dir.normalized()
	
	# Nastavení rychlosti
	velocity = input_dir * game_manager.PLAYER_SPEED
	
	# Pohyb
	move_and_slide()
	
	# Omezení na obrazovku
	position.x = clamp(position.x, game_manager.PLAYER_SIZE / 2.0, game_manager.SCREEN_WIDTH - game_manager.PLAYER_SIZE / 2.0)
	position.y = clamp(position.y, game_manager.PLAYER_SIZE / 2.0, game_manager.SCREEN_HEIGHT - game_manager.PLAYER_SIZE / 2.0)

func _input(event):
	if event.is_action_pressed("shoot"):
		shoot()

func shoot():
	print("🔫 Player střílí z pozice: ", position)
	
	# Vytvoření střely
	var bullet_scene = preload("res://scenes/bullet.tscn")
	var bullet = bullet_scene.instantiate()
	
	# Přidání střely do scény
	get_parent().add_child(bullet)
	
	# Nastavení pozice střely
	bullet.position = position
	
	# Nastavení směru střely (směrem k myši)
	var mouse_pos = get_global_mouse_position()
	var direction = (mouse_pos - position).normalized()
	bullet.set_direction(direction)

func take_damage(damage: int):
	health -= damage
	print("💔 Player dostal poškození: ", damage, " (zdraví: ", health, "/", max_health, ")")
	
	# Vizuální efekt poškození
	sprite.modulate = Color.RED
	var tween = create_tween()
	tween.tween_property(sprite, "modulate", Color.WHITE, 0.2)
	
	# Kontrola smrti
	if health <= 0:
		die()

func die():
	print("💀 Player zemřel!")
	game_manager.change_state(GameManager.GameState.GAME_OVER)
	queue_free()
