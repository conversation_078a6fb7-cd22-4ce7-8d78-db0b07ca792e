extends CharacterBody2D

@onready var game_manager = GameManager
@onready var sprite = $Sprite2D
@onready var light = $PointLight2D
@onready var light_area = $LightArea
@onready var light_collision = $LightArea/CollisionShape2D

var health: int
var max_health: int

# Systém střelby
var last_shot_time = 0.0
var can_shoot = true

func _ready():
	print("👤 Player inicializován")

	# Nastavení zdraví
	max_health = game_manager.PLAYER_MAX_HEALTH
	health = max_health

	# Nastavení pozice na střed obrazovky
	position = Vector2(game_manager.SCREEN_WIDTH / 2.0, game_manager.SCREEN_HEIGHT / 2.0)

	print("   Pozice: ", position)
	print("   Zdraví: ", health, "/", max_health)

func _process(delta):
	# Update světelného systému
	game_manager.update_light(delta)
	game_manager.update_status_effects(delta)
	update_light_visual()

func _physics_process(_delta):
	# <PERSON>ískání vstupu
	var input_dir = Vector2.ZERO

	if Input.is_action_pressed("move_up"):
		input_dir.y -= 1
	if Input.is_action_pressed("move_down"):
		input_dir.y += 1
	if Input.is_action_pressed("move_left"):
		input_dir.x -= 1
	if Input.is_action_pressed("move_right"):
		input_dir.x += 1

	# Normalizace směru
	if input_dir.length() > 0:
		input_dir = input_dir.normalized()

	# Nastavení rychlosti s speed multiplierem
	var speed = game_manager.PLAYER_SPEED * game_manager.get_speed_multiplier()
	velocity = input_dir * speed

	# Pohyb
	move_and_slide()

	# Omezení na obrazovku
	position.x = clamp(position.x, game_manager.PLAYER_SIZE / 2.0, game_manager.SCREEN_WIDTH - game_manager.PLAYER_SIZE / 2.0)
	position.y = clamp(position.y, game_manager.PLAYER_SIZE / 2.0, game_manager.SCREEN_HEIGHT - game_manager.PLAYER_SIZE / 2.0)

func update_light_visual():
	# SVĚTLO JE NYNÍ ŘEŠENO SHADEREM V GAME_SCENE - jen kolize
	var current_radius = game_manager.light_radius

	# Nastavení kolizní oblasti světla
	if light_collision.shape is CircleShape2D:
		(light_collision.shape as CircleShape2D).radius = current_radius

func _input(event):
	if event.is_action_pressed("shoot") and can_shoot:
		shoot()

	# PŘEPÍNÁNÍ ZBRANÍ PODLE PYTHON VERZE (1, 2, 3)
	if event.is_action_pressed("weapon_1"):
		game_manager.switch_weapon(GameManager.WeaponType.PISTOL)
	elif event.is_action_pressed("weapon_2"):
		game_manager.switch_weapon(GameManager.WeaponType.SHOTGUN)
	elif event.is_action_pressed("weapon_3"):
		game_manager.switch_weapon(GameManager.WeaponType.MACHINEGUN)

	# Přepínání zbraní (Enter/Space pro cyklování)
	if event.is_action_pressed("ui_accept"):
		cycle_weapon()

func shoot():
	var weapon_data = game_manager.get_current_weapon_data()
	print("🔫 Player střílí ", weapon_data["name"], " z pozice: ", position)

	# Kontrola fire rate (PODLE PYTHON VERZE)
	var current_time = Time.get_ticks_msec() / 1000.0
	# Python verze: BASE_BULLET_COOLDOWN / (fire_rate_multiplier * weapon_fire_rate)
	var fire_cooldown = game_manager.BASE_BULLET_COOLDOWN / weapon_data["fire_rate"]

	if current_time - last_shot_time < fire_cooldown:
		return

	last_shot_time = current_time

	# Směr k myši
	var mouse_pos = get_global_mouse_position()
	var base_direction = (mouse_pos - position).normalized()

	# MUZZLE FLASH EFEKT (PODLE PYTHON VERZE)
	create_muzzle_flash(base_direction, weapon_data)

	# Vytvoření střel podle typu zbraně
	var bullet_count = weapon_data["bullet_count"]
	var spread = weapon_data["spread"]

	for i in range(bullet_count):
		var bullet_scene = preload("res://scenes/bullet.tscn")
		var bullet = bullet_scene.instantiate()

		# Přidání střely do scény
		get_parent().add_child(bullet)

		# Nastavení pozice střely
		bullet.position = position

		# Výpočet směru s rozptylem
		var angle_offset = 0.0
		if bullet_count > 1:
			# Rozptyl pro více střel (shotgun)
			var spread_range = deg_to_rad(spread)
			angle_offset = lerp(-spread_range/2, spread_range/2, float(i) / (bullet_count - 1))
		else:
			# Náhodný rozptyl pro jednu střelu (machine gun)
			if spread > 0:
				angle_offset = randf_range(-deg_to_rad(spread/2), deg_to_rad(spread/2))

		# Aplikace rotace
		var final_direction = base_direction.rotated(angle_offset)

		# Nastavení vlastností střely
		bullet.set_direction(final_direction)
		bullet.setup_weapon_stats(weapon_data)

func take_damage(damage: int):
	health -= damage
	print("💔 Player dostal poškození: ", damage, " (zdraví: ", health, "/", max_health, ")")

	# Vizuální efekt poškození
	sprite.modulate = Color.RED
	var tween = create_tween()
	tween.tween_property(sprite, "modulate", Color.WHITE, 0.2)

	# Kontrola smrti
	if health <= 0:
		die()

func die():
	print("💀 Player zemřel!")
	game_manager.change_state(GameManager.GameState.GAME_OVER)
	queue_free()

func cycle_weapon():
	var owned_weapons = game_manager.owned_weapons
	if owned_weapons.size() <= 1:
		return

	var current_index = owned_weapons.find(game_manager.current_weapon)
	var next_index = (current_index + 1) % owned_weapons.size()
	var next_weapon = owned_weapons[next_index]

	game_manager.switch_weapon(next_weapon)

func create_muzzle_flash(direction: Vector2, weapon_data: Dictionary):
	# MUZZLE FLASH PODLE PYTHON VERZE
	var flash_size = weapon_data.get("bullet_size", 8) * 2
	var flash_distance = game_manager.PLAYER_SIZE / 2
	var flash_pos = position + direction * flash_distance

	# Vytvoření flash efektu
	var flash = ColorRect.new()
	flash.size = Vector2(flash_size * 2, flash_size * 2)
	flash.position = flash_pos - flash.size / 2
	flash.color = Color(1.0, 1.0, 0.8, 0.8)  # Žlutobílá barva s průhledností

	# Přidání do scény
	get_parent().add_child(flash)

	# Animace zmizení
	var tween = create_tween()
	tween.tween_property(flash, "modulate:a", 0.0, 0.1)
	tween.tween_callback(flash.queue_free)
