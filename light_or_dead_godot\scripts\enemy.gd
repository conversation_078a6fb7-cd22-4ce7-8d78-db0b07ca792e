extends CharacterBody2D

@onready var game_manager = GameManager
@onready var sprite = $Sprite2D

var health: int
var max_health: int
var speed: float
var damage: int
var score_value = 10

var player: Node2D = null

func _ready():
	print("👹 Enemy vytvořen na pozici: ", position)

	# Nastavení vlastností
	max_health = game_manager.ENEMY_HEALTH
	health = max_health
	speed = game_manager.ENEMY_SPEED
	damage = 20  # Poškození při kontaktu s hráčem

	print("   Zdraví: ", health, "/", max_health)
	print("   Rychlost: ", speed)

func _physics_process(delta):
	# Najít hráče
	if player == null:
		var players = get_tree().get_nodes_in_group("player")
		if players.size() > 0:
			player = players[0]

	# <PERSON>hyb směrem k hráči
	if player != null:
		var direction = (player.position - position).normalized()
		velocity = direction * speed
		move_and_slide()

		# Kontrola kolize s hráčem
		for i in get_slide_collision_count():
			var collision = get_slide_collision(i)
			var collider = collision.get_collider()

			if collider != null and collider.is_in_group("player"):
				print("👹 Enemy zasáhl hráče!")
				collider.take_damage(damage)
				die()  # Nepřítel zmizí po zásahu hráče

func take_damage(damage_amount: int):
	health -= damage_amount
	print("👹 Enemy dostal poškození: ", damage_amount, " (zdraví: ", health, "/", max_health, ")")

	# Vizuální efekt poškození
	sprite.modulate = Color.YELLOW
	var tween = create_tween()
	tween.tween_property(sprite, "modulate", Color.WHITE, 0.1)

	# Kontrola smrti
	if health <= 0:
		die()

func die():
	print("👹 Enemy zemřel! Skóre +", score_value)
	game_manager.add_score(score_value)

	# Přidání světla za zabití nepřítele - klíčová mechanika!
	game_manager.add_light(game_manager.LIGHT_GAIN_PER_KILL)

	# Oznámení wave systému o zabití nepřítele
	game_manager.enemy_killed()

	queue_free()

func setup_for_wave(wave: int):
	# Nastavení statistik podle vlny
	var stats = game_manager.get_enemy_stats_for_wave(wave)

	max_health = stats["health"]
	health = max_health
	speed = stats["speed"]
	damage = stats["damage"]
	score_value = stats["score"]

	print("👹 Enemy nastaven pro vlnu ", wave, " - HP:", health, " Speed:", speed, " Damage:", damage)
