extends CharacterBody2D

@onready var game_manager = get_node("/root/GameManager")
@onready var sprite = $Sprite2D
@onready var health_bar = $HealthBar
@onready var shoot_timer = $ShootTimer

var health = 3
var max_health = 3
var speed = 60
var damage = 10
var score_value = 10
var is_boss = false
var is_minion = false
var can_shoot = false
var shoot_cooldown = 2.0
var bullet_speed = 150
var bullet_damage = 5

# Boss specific variables
var boss_action_cooldown = 0.0
var boss_is_dashing = false
var boss_dash_timer = 0.0
var boss_dash_direction = Vector2.ZERO

func _ready():
	# Set up the health bar
	health_bar.max_value = max_health
	health_bar.value = health
	
	# Determine if this enemy can shoot
	if game_manager.current_wave >= 3 and randf() < 0.2:
		can_shoot = true
		shoot_timer.wait_time = shoot_cooldown
		shoot_timer.start()
	
	# Add to the appropriate groups
	add_to_group("enemies")
	if is_boss:
		add_to_group("bosses")

func _process(delta):
	# Update health bar
	health_bar.value = health
	
	# Boss specific behavior
	if is_boss:
		handle_boss_behavior(delta)
	else:
		# Regular enemy behavior - move towards player
		var player = get_tree().get_nodes_in_group("player")[0] if get_tree().get_nodes_in_group("player").size() > 0 else null
		
		if player:
			var direction = (player.global_position - global_position).normalized()
			velocity = direction * speed
			
			# Rotate to face player
			rotation = direction.angle()

func _physics_process(delta):
	move_and_slide()

func handle_boss_behavior(delta):
	var player = get_tree().get_nodes_in_group("player")[0] if get_tree().get_nodes_in_group("player").size() > 0 else null
	
	if not player:
		return
	
	if boss_is_dashing:
		# Continue dash movement
		velocity = boss_dash_direction * game_manager.BOSS_DASH_SPEED
		
		boss_dash_timer -= delta
		if boss_dash_timer <= 0:
			boss_is_dashing = false
			boss_action_cooldown = randf_range(game_manager.BOSS_ACTION_COOLDOWN_MIN, game_manager.BOSS_ACTION_COOLDOWN_MAX)
	else:
		# Normal movement towards player
		var direction = (player.global_position - global_position).normalized()
		velocity = direction * speed
		
		# Rotate to face player
		rotation = direction.angle()
		
		# Check if it's time for a special action
		boss_action_cooldown -= delta
		if boss_action_cooldown <= 0:
			perform_boss_action(player)

func perform_boss_action(player):
	# Choose a random action
	var action = randi() % 3
	
	match action:
		0:  # Dash attack
			boss_is_dashing = true
			boss_dash_timer = game_manager.BOSS_DASH_DURATION
			boss_dash_direction = (player.global_position - global_position).normalized()
		
		1:  # Projectile attack
			# Shoot multiple projectiles in a pattern
			var num_projectiles = 8
			for i in range(num_projectiles):
				var angle = 2 * PI * i / num_projectiles
				var direction = Vector2(cos(angle), sin(angle))
				
				var bullet = preload("res://scenes/enemy_bullet.tscn").instantiate()
				get_parent().add_child(bullet)
				bullet.global_position = global_position
				bullet.rotation = angle
				bullet.speed = game_manager.BOSS_PROJECTILE_SPEED
				bullet.damage = game_manager.BOSS_PROJECTILE_DAMAGE
				bullet.from_player = false
		
		2:  # Summon minions
			var count = randi_range(game_manager.BOSS_SUMMON_COUNT_MIN, game_manager.BOSS_SUMMON_COUNT_MAX)
			summon_minions(count)
	
	# Set cooldown for next action
	boss_action_cooldown = randf_range(game_manager.BOSS_ACTION_COOLDOWN_MIN, game_manager.BOSS_ACTION_COOLDOWN_MAX)

func summon_minions(count):
	for i in range(count):
		var minion = load("res://scenes/enemy.tscn").instantiate()
		get_parent().add_child(minion)
		
		# Calculate spawn position around the boss
		var angle = randf_range(0, 2 * PI)
		var distance = randf_range(50, 100)
		var spawn_pos = global_position + Vector2(cos(angle), sin(angle)) * distance
		
		# Ensure spawn position is within screen bounds
		spawn_pos.x = clamp(spawn_pos.x, 0, game_manager.SCREEN_WIDTH)
		spawn_pos.y = clamp(spawn_pos.y, 0, game_manager.SCREEN_HEIGHT)
		
		minion.global_position = spawn_pos
		minion.is_minion = true
		
		# Set minion properties
		var minion_health = int(max_health * game_manager.BOSS_SUMMON_HEALTH_FACTOR)
		minion.health = max(1, minion_health)
		minion.max_health = minion.health
		minion.speed = speed * 1.2  # Minions are a bit faster
		minion.damage = damage * 0.7  # But do less damage
		minion.score_value = score_value * 0.5  # Worth less score
		
		# Update game manager's enemy count
		game_manager.enemies_alive_in_wave += 1

func take_damage(amount):
	health -= amount
	
	# Flash the sprite to indicate damage
	sprite.modulate = Color(1, 0.5, 0.5, 1)
	var tween = create_tween()
	tween.tween_property(sprite, "modulate", Color(1, 1, 1, 1), 0.2)
	
	if health <= 0:
		die()

func die():
	# Add score
	game_manager.score += score_value
	
	# Add light to player
	var player = get_tree().get_nodes_in_group("player")[0] if get_tree().get_nodes_in_group("player").size() > 0 else null
	if player:
		var light_gain = game_manager.LIGHT_GAIN_PER_KILL
		if is_boss:
			light_gain = game_manager.BOSS_LIGHT_REWARD
		game_manager.player_stats["light_to_add"] += light_gain
	
	# Update enemy count
	game_manager.enemies_alive_in_wave -= 1
	
	# Check if wave is complete
	if game_manager.enemies_alive_in_wave <= 0 and game_manager.enemies_to_spawn_in_wave <= 0:
		game_manager.wave_transition_timer = game_manager.WAVE_TRANSITION_DELAY
		game_manager.wave_message_text = "Wave Complete!"
		game_manager.wave_message_timer = game_manager.WAVE_MESSAGE_DURATION
	
	# Play death effect
	# $DeathParticles.emitting = true
	
	# Remove the enemy
	queue_free()

func shoot():
	if not can_shoot:
		return
	
	var player = get_tree().get_nodes_in_group("player")[0] if get_tree().get_nodes_in_group("player").size() > 0 else null
	if player:
		var bullet = preload("res://scenes/enemy_bullet.tscn").instantiate()
		get_parent().add_child(bullet)
		
		bullet.global_position = global_position
		var direction = (player.global_position - global_position).normalized()
		bullet.rotation = direction.angle()
		bullet.speed = bullet_speed
		bullet.damage = bullet_damage
		bullet.from_player = false

func _on_ShootTimer_timeout():
	shoot()
	shoot_timer.wait_time = shoot_cooldown
	shoot_timer.start()
