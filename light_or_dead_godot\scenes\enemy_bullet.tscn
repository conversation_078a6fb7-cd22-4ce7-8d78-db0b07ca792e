[gd_scene load_steps=4 format=3]

[ext_resource type="Script" path="res://scripts/bullet.gd" id="1"]

[sub_resource type="CircleShape2D" id="1"]
radius = 8.0

[sub_resource type="Gradient" id="2"]
colors = PackedColorArray(1, 0.5, 0, 1, 1, 0, 0, 0)

[node name="EnemyBullet" type="Area2D" groups=["bullets"]]
script = ExtResource("1")

[node name="Sprite2D" type="Sprite2D" parent="."]
modulate = Color(1, 0.5, 0, 1)
scale = Vector2(0.8, 0.8)

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("1")

[node name="CPUParticles2D" type="CPUParticles2D" parent="."]
amount = 15
lifetime = 0.5
local_coords = false
direction = Vector2(-1, 0)
spread = 10.0
gravity = Vector2(0, 0)
initial_velocity_min = 20.0
initial_velocity_max = 40.0
scale_amount_min = 2.0
scale_amount_max = 3.0
color_ramp = SubResource("2")
