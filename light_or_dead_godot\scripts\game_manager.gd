extends Node

# Her<PERSON><PERSON> stavy
enum GameState {
	MENU,
	PLAYING,
	PAUSED,
	GAME_OVER
}

# Konst<PERSON><PERSON> obra<PERSON>vky
const SCREEN_WIDTH = 800
const SCREEN_HEIGHT = 600

# Konstanty hráče
const PLAYER_SIZE = 30
const PLAYER_SPEED = 200.0
const PLAYER_MAX_HEALTH = 100

# Konstanty světla - klíčová mechanika!
const INITIAL_LIGHT_RADIUS = 180.0
const MAX_LIGHT_RADIUS = 450.0
const MIN_LIGHT_RADIUS = 35.0
const LIGHT_DEATH_THRESHOLD = -1.0  # Opraveno - hráč nezemře tak rychle
const LIGHT_SHRINK_RATE = 3.0       # Zpomaleno zmenšování (bylo 15.0)
const LIGHT_GAIN_PER_KILL = 8.0     # Podle Python verze
const LIGHT_FLICKER_INTENSITY = 1.5 # S<PERSON><PERSON><PERSON><PERSON> blik<PERSON><PERSON>
const LIGHT_GROW_INTERP_FACTOR = 16.0

# Konst<PERSON>y střel
const BULLET_SIZE = 8
const BULLET_SPEED = 400.0
const BULLET_DAMAGE = 10

# Konstanty nepřátel
const ENEMY_SIZE = 24
const ENEMY_SPEED = 80.0
const ENEMY_HEALTH = 30

# Konstanty pastí
const TRAP_SIZE = 20
const POISON_TRAP_DAMAGE = 5
const POISON_DURATION = 3.0
const SLOW_TRAP_FACTOR = 0.5
const SLOW_DURATION = 2.0
const SPIKE_TRAP_DAMAGE = 15

# Typy zbraní
enum WeaponType {
	PISTOL,
	SHOTGUN,
	MACHINEGUN
}

# Typy pastí
enum TrapType {
	POISON,
	SLOW,
	SPIKE
}

# Konstanty zbraní
const WEAPON_DATA = {
	WeaponType.PISTOL: {
		"name": "Pistol",
		"damage": 10,
		"fire_rate": 1.0,
		"bullet_speed": 400.0,
		"bullet_count": 1,
		"spread": 0.0,
		"cost": 0
	},
	WeaponType.SHOTGUN: {
		"name": "Shotgun",
		"damage": 6,
		"fire_rate": 0.6,
		"bullet_speed": 350.0,
		"bullet_count": 5,
		"spread": 25.0,
		"cost": 300
	},
	WeaponType.MACHINEGUN: {
		"name": "Machine Gun",
		"damage": 7,
		"fire_rate": 3.0,
		"bullet_speed": 450.0,
		"bullet_count": 1,
		"spread": 8.0,
		"cost": 500
	}
}

# Herní proměnné
var current_state = GameState.MENU
var score = 0
var player_health = PLAYER_MAX_HEALTH

# Světelný systém
var light_radius = INITIAL_LIGHT_RADIUS
var light_to_add = 0.0  # Buffer pro plynulé přidávání světla

# Wave systém
var current_wave = 0
var enemies_in_wave = 0
var enemies_killed_in_wave = 0
var wave_active = false

# Systém zbraní
var current_weapon = WeaponType.PISTOL
var owned_weapons = [WeaponType.PISTOL]  # Hráč začíná s pistolí

# Player status efekty
var player_poisoned = false
var poison_timer = 0.0
var player_slowed = false
var slow_timer = 0.0
var speed_multiplier = 1.0

# Shop systém
var shop_items = [
	{
		"id": "health",
		"name": "Health Pack",
		"cost": 50,
		"description": "+40 HP",
		"type": "consumable"
	},
	{
		"id": "light",
		"name": "Light Radius",
		"cost": 75,
		"description": "+50 Radius",
		"type": "consumable"
	},
	{
		"id": "shotgun",
		"name": "Shotgun",
		"cost": 300,
		"description": "5 bullets, wide spread",
		"type": "weapon"
	},
	{
		"id": "machinegun",
		"name": "Machine Gun",
		"cost": 500,
		"description": "Fast fire rate",
		"type": "weapon"
	}
]

func _ready():
	print("✅ GameManager inicializován")
	print("📊 Obrazovka: ", SCREEN_WIDTH, "x", SCREEN_HEIGHT)

func change_state(new_state: GameState):
	print("🔄 Změna stavu z ", current_state, " na ", new_state)
	current_state = new_state

func add_score(points: int):
	score += points
	print("🎯 Skóre: ", score, " (+", points, ")")

func damage_player(damage: int):
	player_health -= damage
	print("💔 Hráč poškození: ", damage, " (zdraví: ", player_health, ")")

	if player_health <= 0:
		print("💀 Hráč zemřel!")
		change_state(GameState.GAME_OVER)

func reset_game():
	print("🔄 Reset hry")
	score = 0
	player_health = PLAYER_MAX_HEALTH
	light_radius = INITIAL_LIGHT_RADIUS
	light_to_add = 0.0
	current_wave = 0
	enemies_in_wave = 0
	enemies_killed_in_wave = 0
	wave_active = false
	current_weapon = WeaponType.PISTOL
	owned_weapons = [WeaponType.PISTOL]
	change_state(GameState.PLAYING)

# Světelný systém
func update_light(delta: float):
	# Postupné přidávání světla
	if light_to_add > 0:
		var add_amount = min(light_to_add, 50.0 * delta)
		light_radius += add_amount
		light_to_add -= add_amount

	# Zmenšování světla v čase
	light_radius -= LIGHT_SHRINK_RATE * delta

	# Omezení na min/max
	light_radius = clamp(light_radius, 0, MAX_LIGHT_RADIUS)

	# Kontrola smrti tmou
	if light_radius <= LIGHT_DEATH_THRESHOLD:
		print("🌑 Hráč zemřel tmou!")
		change_state(GameState.GAME_OVER)

func add_light(amount: float):
	light_to_add += amount
	print("💡 Přidáno světlo: +", amount, " (celkem k přidání: ", light_to_add, ")")

func get_light_percentage() -> float:
	return light_radius / MAX_LIGHT_RADIUS

# Wave systém
func start_wave(wave_number: int):
	current_wave = wave_number
	enemies_killed_in_wave = 0

	# Výpočet počtu nepřátel podle vlny
	enemies_in_wave = 3 + (wave_number * 2)  # 5, 7, 9, 11...

	wave_active = true
	print("🌊 Začíná vlna ", wave_number, " s ", enemies_in_wave, " nepřáteli")

func enemy_killed():
	enemies_killed_in_wave += 1
	print("👹 Nepřítel zabit (", enemies_killed_in_wave, "/", enemies_in_wave, ")")

	# Kontrola konce vlny
	if enemies_killed_in_wave >= enemies_in_wave:
		complete_wave()

func complete_wave():
	wave_active = false
	print("✅ Vlna ", current_wave, " dokončena!")

	# Bonus světla za dokončení vlny
	var wave_bonus = 20.0 + (current_wave * 5.0)
	add_light(wave_bonus)
	print("🎁 Bonus světla za vlnu: +", wave_bonus)

func get_enemy_stats_for_wave(wave: int) -> Dictionary:
	# Škálování nepřátel podle vlny
	return {
		"health": ENEMY_HEALTH + (wave * 10),
		"speed": ENEMY_SPEED + (wave * 10.0),
		"damage": 20 + (wave * 5),
		"score": 10 + (wave * 2)
	}

# Systém zbraní
func get_current_weapon_data() -> Dictionary:
	return WEAPON_DATA[current_weapon]

func switch_weapon(weapon_type: WeaponType):
	if weapon_type in owned_weapons:
		current_weapon = weapon_type
		print("🔫 Přepnuto na zbraň: ", WEAPON_DATA[weapon_type]["name"])
		return true
	else:
		print("❌ Zbraň není vlastněna: ", WEAPON_DATA[weapon_type]["name"])
		return false

func buy_weapon(weapon_type: WeaponType) -> bool:
	var weapon_data = WEAPON_DATA[weapon_type]

	if weapon_type in owned_weapons:
		print("❌ Zbraň již vlastníte: ", weapon_data["name"])
		return false

	if score >= weapon_data["cost"]:
		score -= weapon_data["cost"]
		owned_weapons.append(weapon_type)
		print("✅ Koupena zbraň: ", weapon_data["name"], " za ", weapon_data["cost"])
		return true
	else:
		print("❌ Nedostatek bodů pro: ", weapon_data["name"], " (potřeba: ", weapon_data["cost"], ")")
		return false

func get_weapon_index(weapon_type: WeaponType) -> int:
	return owned_weapons.find(weapon_type)

# Shop systém
func buy_shop_item(item_id: String) -> bool:
	var item = null
	for shop_item in shop_items:
		if shop_item["id"] == item_id:
			item = shop_item
			break

	if not item:
		print("❌ Položka nenalezena: ", item_id)
		return false

	if score < item["cost"]:
		print("❌ Nedostatek bodů pro: ", item["name"], " (potřeba: ", item["cost"], ")")
		return false

	# Kontrola pro zbraně
	if item["type"] == "weapon":
		if item_id == "shotgun":
			if WeaponType.SHOTGUN in owned_weapons:
				print("❌ Shotgun již vlastníte")
				return false
			score -= item["cost"]
			owned_weapons.append(WeaponType.SHOTGUN)
			print("✅ Koupena zbraň: Shotgun")
			return true
		elif item_id == "machinegun":
			if WeaponType.MACHINEGUN in owned_weapons:
				print("❌ Machine Gun již vlastníte")
				return false
			score -= item["cost"]
			owned_weapons.append(WeaponType.MACHINEGUN)
			print("✅ Koupena zbraň: Machine Gun")
			return true

	# Spotřební předměty
	elif item["type"] == "consumable":
		score -= item["cost"]

		if item_id == "health":
			player_health = min(PLAYER_MAX_HEALTH, player_health + 40)
			print("✅ Koupeno: Health Pack (+40 HP)")
			return true
		elif item_id == "light":
			add_light(50.0)
			print("✅ Koupeno: Light Radius (+50)")
			return true

	return false

func can_buy_item(item_id: String) -> bool:
	var item = null
	for shop_item in shop_items:
		if shop_item["id"] == item_id:
			item = shop_item
			break

	if not item:
		return false

	if score < item["cost"]:
		return false

	# Kontrola pro zbraně
	if item["type"] == "weapon":
		if item_id == "shotgun" and WeaponType.SHOTGUN in owned_weapons:
			return false
		if item_id == "machinegun" and WeaponType.MACHINEGUN in owned_weapons:
			return false

	return true

# Status efekty
func apply_poison():
	player_poisoned = true
	poison_timer = POISON_DURATION
	print("☠️ Hráč otráven na ", POISON_DURATION, " sekund!")

func apply_slow():
	player_slowed = true
	slow_timer = SLOW_DURATION
	speed_multiplier = SLOW_TRAP_FACTOR
	print("🐌 Hráč zpomalen na ", SLOW_DURATION, " sekund!")

func update_status_effects(delta: float):
	# Aktualizace otravy
	if player_poisoned:
		poison_timer -= delta

		# Poškození každou sekundu
		var damage_interval = 1.0
		if fmod(poison_timer, damage_interval) < delta:
			damage_player(POISON_TRAP_DAMAGE)
			print("☠️ Poškození otravou: ", POISON_TRAP_DAMAGE)

		if poison_timer <= 0:
			player_poisoned = false
			print("✅ Otrava pominula")

	# Aktualizace zpomalení
	if player_slowed:
		slow_timer -= delta
		if slow_timer <= 0:
			player_slowed = false
			speed_multiplier = 1.0
			print("✅ Zpomalení pominulo")

func get_speed_multiplier() -> float:
	return speed_multiplier