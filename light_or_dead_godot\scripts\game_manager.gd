extends Node

# Game States
enum GameState {SPLASH, MAIN_MENU, PLAYING, SHOP, GAME_OVER, SETTINGS}

# Trap Types
enum TrapType {SPIKE, POISON, SLOW}

# Weapon Types
enum WeaponType {PISTOL, SHOTGUN, MACHINEGUN}

# Constants
const SCREEN_WIDTH = 800
const SCREEN_HEIGHT = 600
const PLAYER_SIZE = 30
const PLAYER_BASE_SPEED = 200
const PLAYER_START_X = SCREEN_WIDTH / 2.0
const PLAYER_START_Y = SCREEN_HEIGHT / 2.0
const PLAYER_MAX_HEALTH = 100

const BULLET_SIZE = 5
const BASE_BULLET_SPEED = 400
const BASE_BULLET_COOLDOWN = 0.25
const BASE_BULLET_DAMAGE = 1

const INITIAL_LIGHT_RADIUS = 180.0
const MAX_LIGHT_RADIUS = 450.0
const MIN_LIGHT_RADIUS = 35.0
const LIGHT_DEATH_THRESHOLD = -1.0
const LIGHT_FLICKER_AMOUNT = 1.5
const LIGHT_SHRINK_RATE = 3.0
const LIGHT_GAIN_PER_KILL = 8.0
const LIGHT_GROW_INTERP_FACTOR = 16.0

const ENEMY_BASE_SIZE = 20
const ENEMY_BASE_SPEED = 60
const ENEMY_BASE_HEALTH = 3
const ENEMY_BASE_DAMAGE = 10
const ENEMY_BASE_SCORE = 10

const TRAP_SPIKE_DAMAGE = 15
const TRAP_SPIKE_SIZE = 24
const TRAP_POISON_DAMAGE = 2
const TRAP_POISON_DURATION = 5.0
const TRAP_POISON_SIZE = 30
const TRAP_SLOW_FACTOR = 0.5
const TRAP_SLOW_DURATION = 3.0
const TRAP_SLOW_SIZE = 28
const TRAP_SPAWN_CHANCE = 0.2

const HIGH_SCORE_FILE = "user://light_or_dead_highscore.dat"

# Game variables
var current_state = GameState.SPLASH
var score = 0
var high_score = 0

# Player stats
var player_stats = {
	"health": PLAYER_MAX_HEALTH,
	"max_health": PLAYER_MAX_HEALTH,
	"light_radius": INITIAL_LIGHT_RADIUS,
	"speed_multiplier": 1.0,
	"fire_rate_multiplier": 1.0,
	"bullet_damage": BASE_BULLET_DAMAGE,
	"light_to_add": 0.0,
	"current_weapon": WeaponType.PISTOL,
	"weapons": [WeaponType.PISTOL],
	"poisoned": false,
	"poison_timer": 0.0,
	"slowed": false,
	"slow_timer": 0.0,
	"challenges_completed": 0
}

# Wave system
var current_wave = 0
var enemies_to_spawn_in_wave = 0
var enemies_alive_in_wave = 0
var wave_transition_timer = 0.0
const WAVE_TRANSITION_DELAY = 4.0
const WAVE_MESSAGE_DURATION = 2.5
var wave_message_timer = 0.0
var wave_message_text = ""

# Weapons data
var weapons = [
	{
		"name": "Pistol",
		"damage": BASE_BULLET_DAMAGE,
		"fire_rate": 1.0,
		"bullet_speed": BASE_BULLET_SPEED,
		"bullet_size": BULLET_SIZE,
		"bullet_count": 1,
		"spread": 0.0,
		"cost": 0,
		"owned": true,
		"description": "Standard pistol"
	},
	{
		"name": "Shotgun",
		"damage": BASE_BULLET_DAMAGE * 0.5,
		"fire_rate": 0.4,
		"bullet_speed": BASE_BULLET_SPEED * 0.8,
		"bullet_size": BULLET_SIZE * 1.2,
		"bullet_count": 7,
		"spread": 15.0,
		"cost": 300,
		"owned": false,
		"description": "Wide spread, 7 bullets"
	},
	{
		"name": "Machine Gun",
		"damage": BASE_BULLET_DAMAGE * 0.4,
		"fire_rate": 4.0,
		"bullet_speed": BASE_BULLET_SPEED * 1.1,
		"bullet_size": BULLET_SIZE * 0.8,
		"bullet_count": 1,
		"spread": 8.0,
		"cost": 500,
		"owned": false,
		"description": "Rapid fire weapon"
	}
]

# Shop items
var shop_items = [
	{
		"id": "health",
		"name": "Health Pack",
		"cost": 50,
		"desc": "+40 HP",
		"effect": "health_pack"
	},
	{
		"id": "light",
		"name": "Light Radius",
		"cost": 75,
		"desc": "+50 Radius (Gradual)",
		"effect": "light_radius"
	},
	{
		"id": "firerate",
		"name": "Fire Rate",
		"cost": 100,
		"desc": "+10% Speed",
		"effect": "fire_rate"
	},
	{
		"id": "bullet_dmg",
		"name": "Bullet Damage",
		"cost": 120,
		"desc": "+1 Damage",
		"effect": "bullet_damage"
	},
	{
		"id": "move_speed",
		"name": "Move Speed",
		"cost": 90,
		"desc": "+10% Speed",
		"effect": "move_speed"
	},
	{
		"id": "max_health",
		"name": "Max Health",
		"cost": 150,
		"desc": "+20 Max HP",
		"effect": "max_health"
	},
	{
		"id": "shotgun",
		"name": "Shotgun",
		"cost": 300,
		"desc": "5 bullets in spread",
		"effect": "buy_shotgun"
	},
	{
		"id": "machinegun",
		"name": "Machine Gun",
		"cost": 500,
		"desc": "Rapid fire weapon",
		"effect": "buy_machinegun"
	}
]

# Settings
var settings = {
	"resolution": Vector2(SCREEN_WIDTH, SCREEN_HEIGHT),
	"show_fps": true,
	"vsync": true,
	"quality": 1,  # 0=Low, 1=Medium, 2=High
	"volume": 0.7,
	"music_volume": 0.5,
	"difficulty": 1  # 0=Easy, 1=Normal, 2=Hard
}

func _ready():
	load_highscore()

# High score functions
func load_highscore():
	var file = FileAccess.open(HIGH_SCORE_FILE, FileAccess.READ)
	if file:
		high_score = int(file.get_line())
		file.close()
	else:
		high_score = 0

func save_highscore():
	var file = FileAccess.open(HIGH_SCORE_FILE, FileAccess.WRITE)
	if file:
		file.store_line(str(score))
		file.close()

# Shop functions
func buy_item(item_id):
	for item in shop_items:
		if item["id"] == item_id:
			if score >= item["cost"]:
				score -= item["cost"]
				apply_item_effect(item["effect"])
				return true
	return false

func apply_item_effect(effect):
	match effect:
		"health_pack":
			player_stats["health"] = min(player_stats["max_health"], player_stats["health"] + 40)
		"light_radius":
			player_stats["light_to_add"] += 50
		"fire_rate":
			player_stats["fire_rate_multiplier"] *= 1.10
		"bullet_damage":
			player_stats["bullet_damage"] += 1
		"move_speed":
			player_stats["speed_multiplier"] *= 1.10
		"max_health":
			player_stats["max_health"] += 20
			player_stats["health"] += 20
		"buy_shotgun":
			weapons[WeaponType.SHOTGUN]["owned"] = true
			if not WeaponType.SHOTGUN in player_stats["weapons"]:
				player_stats["weapons"].append(WeaponType.SHOTGUN)
		"buy_machinegun":
			weapons[WeaponType.MACHINEGUN]["owned"] = true
			if not WeaponType.MACHINEGUN in player_stats["weapons"]:
				player_stats["weapons"].append(WeaponType.MACHINEGUN)

# Game state management
func change_game_state(new_state):
	current_state = new_state

# Reset game for new play
func reset_game():
	score = 0
	current_wave = 0
	enemies_to_spawn_in_wave = 0
	enemies_alive_in_wave = 0
	wave_transition_timer = 0.0
	wave_message_timer = 0.0
	wave_message_text = ""

	# Reset player stats
	player_stats = {
		"health": PLAYER_MAX_HEALTH,
		"max_health": PLAYER_MAX_HEALTH,
		"light_radius": INITIAL_LIGHT_RADIUS,
		"speed_multiplier": 1.0,
		"fire_rate_multiplier": 1.0,
		"bullet_damage": BASE_BULLET_DAMAGE,
		"light_to_add": 0.0,
		"current_weapon": WeaponType.PISTOL,
		"weapons": [WeaponType.PISTOL],
		"poisoned": false,
		"poison_timer": 0.0,
		"slowed": false,
		"slow_timer": 0.0,
		"challenges_completed": 0
	}

	# Reset weapons ownership
	weapons[WeaponType.PISTOL]["owned"] = true
	weapons[WeaponType.SHOTGUN]["owned"] = false
	weapons[WeaponType.MACHINEGUN]["owned"] = false
