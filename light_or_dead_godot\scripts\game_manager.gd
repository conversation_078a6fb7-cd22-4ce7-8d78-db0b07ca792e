extends Node

# Hern<PERSON> stavy
enum GameState {
	MENU,
	PLAYING,
	PAUSED,
	GAME_OVER
}

# <PERSON>nstant<PERSON> obrazovky
const SCREEN_WIDTH = 800
const SCREEN_HEIGHT = 600

# Konstanty hráče
const PLAYER_SIZE = 30
const PLAYER_SPEED = 200.0
const PLAYER_MAX_HEALTH = 100

# Konstanty střel
const BULLET_SIZE = 8
const BULLET_SPEED = 400.0
const BULLET_DAMAGE = 10

# Konstanty nepřátel
const ENEMY_SIZE = 24
const ENEMY_SPEED = 80.0
const ENEMY_HEALTH = 30

# Herní proměnné
var current_state = GameState.MENU
var score = 0
var player_health = PLAYER_MAX_HEALTH

func _ready():
	print("✅ GameManager inicializován")
	print("📊 Obrazovka: ", SCREEN_WIDTH, "x", SCREEN_HEIGHT)

func change_state(new_state: GameState):
	print("🔄 Změna stavu z ", current_state, " na ", new_state)
	current_state = new_state

func add_score(points: int):
	score += points
	print("🎯 Skóre: ", score, " (+", points, ")")

func damage_player(damage: int):
	player_health -= damage
	print("💔 Hráč poškození: ", damage, " (zdraví: ", player_health, ")")
	
	if player_health <= 0:
		print("💀 Hráč zemřel!")
		change_state(GameState.GAME_OVER)

func reset_game():
	print("🔄 Reset hry")
	score = 0
	player_health = PLAYER_MAX_HEALTH
	change_state(GameState.PLAYING)
