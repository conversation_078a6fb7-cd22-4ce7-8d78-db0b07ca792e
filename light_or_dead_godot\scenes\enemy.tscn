[gd_scene load_steps=3 format=3]

[ext_resource type="Script" path="res://scripts/enemy.gd" id="1"]

[sub_resource type="CircleShape2D" id="1"]
radius = 10.0

[node name="Enemy" type="CharacterBody2D" groups=["enemies"]]
script = ExtResource("1")

[node name="Sprite2D" type="Sprite2D" parent="."]
modulate = Color(1, 0, 0, 1)
scale = Vector2(0.4, 0.4)

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("1")

[node name="HealthBar" type="ProgressBar" parent="."]
modulate = Color(1, 0, 0, 0.7)
offset_left = -15.0
offset_top = -25.0
offset_right = 15.0
offset_bottom = -20.0
max_value = 3.0
value = 3.0
show_percentage = false

[node name="ShootTimer" type="Timer" parent="."]
wait_time = 2.0
one_shot = true
