[gd_scene load_steps=4 format=3 uid="uid://enemy_scene"]

[ext_resource type="Script" path="res://scripts/enemy.gd" id="1_enemy"]
[ext_resource type="Texture2D" uid="uid://enemy_texture" path="res://assets/images/enemy.png" id="2_enemy"]

[sub_resource type="CircleShape2D" id="CircleShape2D_1"]
radius = 12.0

[node name="Enemy" type="CharacterBody2D" groups=["enemies"]]
script = ExtResource("1_enemy")

[node name="Sprite2D" type="Sprite2D" parent="."]
texture = ExtResource("2_enemy")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("CircleShape2D_1")
