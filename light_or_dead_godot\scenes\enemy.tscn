[gd_scene load_steps=5 format=3 uid="uid://b3f8cwoql4n7a"]

[ext_resource type="Script" path="res://scripts/enemy.gd" id="1_enemy"]
[ext_resource type="Texture2D" path="res://assets/images/enemy.png" id="2_enemy"]

[sub_resource type="CircleShape2D" id="CircleShape2D_1"]
radius = 12.0

[sub_resource type="CircleShape2D" id="CircleShape2D_2"]
radius = 15.0

[node name="Enemy" type="CharacterBody2D" groups=["enemies"]]
script = ExtResource("1_enemy")

[node name="Sprite2D" type="Sprite2D" parent="."]
texture = ExtResource("2_enemy")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("CircleShape2D_1")

# AREA2D PRO DETEKCI STŘEL
[node name="HitArea" type="Area2D" parent="."]

[node name="HitCollision" type="CollisionShape2D" parent="HitArea"]
shape = SubResource("CircleShape2D_2")
